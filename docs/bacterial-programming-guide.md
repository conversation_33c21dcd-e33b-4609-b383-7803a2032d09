# The Bacterial Programming Developer Bible 🦠

*A guide to writing code that spreads like wildfire through horizontal gene transfer*

## Table of Contents
- [Philosophy](#philosophy)
- [Core Principles](#core-principles)
- [The Bacterial Code Checklist](#the-bacterial-code-checklist)
- [Practical Guidelines](#practical-guidelines)
- [Code Examples](#code-examples)
- [Anti-Patterns to Avoid](#anti-patterns-to-avoid)
- [When to Go Eukaryotic](#when-to-go-eukaryotic)
- [Tools and Techniques](#tools-and-techniques)

## Philosophy

> "How to build a thriving open source community by writing code like bacteria do 🦠"
> — Andrej <PERSON>athy

Bacterial code mimics how bacteria have colonized every ecological nook on Earth through horizontal gene transfer. Your code should be so clean, modular, and self-contained that anyone can "yoink" it without understanding your entire codebase.

### The Biological Analogy

**Bacterial Genomes:**
- Small (each gene costs metabolic energy)
- Modular (organized into swappable operons)
- Self-contained (easily transferable via horizontal gene transfer)
- Highly adaptable and inventive

**Eukaryotic Genomes:**
- Larger, more complex and coupled
- Better for coordinated, complex systems
- Less inventive but necessary for sophisticated organisms

## Core Principles

### 1. **Energy Efficiency** 💡
Every line of code must justify its existence. If it doesn't add clear value, delete it.

### 2. **Modularity** 🧩
Organize code into independent, swappable units that can function in isolation.

### 3. **Self-Containment** 📦
Functions and classes should have minimal external dependencies and be easily extractable.

### 4. **Copy-Paste Friendliness** 📋
Code should be so clean that others can copy it without needing to understand your entire system.

## The Bacterial Code Checklist

Before committing any function or class, ask yourself:

- [ ] **The Yoink Test**: Can someone copy this code and use it immediately without importing anything new?
- [ ] **The Gist Test**: Could this code be a trending GitHub gist?
- [ ] **The Energy Test**: Does every line serve a clear purpose?
- [ ] **The Isolation Test**: Can this code run independently of the rest of my system?
- [ ] **The Documentation Test**: Is the purpose and usage immediately clear from reading the code?

## Practical Guidelines

### Function Design

```typescript
// ✅ GOOD: Bacterial function
function formatCurrency(amount: number, currency = 'USD'): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
  }).format(amount);
}

// ❌ BAD: Eukaryotic function (too coupled)
function formatPrice(productId: string): string {
  const product = this.productService.getById(productId);
  const userCurrency = this.userService.getCurrentUser().currency;
  const exchangeRate = this.currencyService.getRate(userCurrency);
  return this.formatWithTax(product.price * exchangeRate, userCurrency);
}
```

### Class Design

```typescript
// ✅ GOOD: Bacterial class (self-contained utility)
class DateRange {
  constructor(
    public readonly start: Date,
    public readonly end: Date
  ) {}

  contains(date: Date): boolean {
    return date >= this.start && date <= this.end;
  }

  overlaps(other: DateRange): boolean {
    return this.start <= other.end && this.end >= other.start;
  }

  getDays(): number {
    return Math.ceil((this.end.getTime() - this.start.getTime()) / (1000 * 60 * 60 * 24));
  }
}

// ❌ BAD: Eukaryotic class (too many dependencies)
class OrderProcessor {
  constructor(
    private paymentService: PaymentService,
    private inventoryService: InventoryService,
    private emailService: EmailService,
    private auditLogger: AuditLogger
  ) {}
  // ... complex interdependent methods
}
```

### File Organization

```
utils/
├── date/
│   ├── formatters.ts      # Pure date formatting functions
│   ├── validators.ts      # Date validation utilities
│   └── calculators.ts     # Date math utilities
├── string/
│   ├── slugify.ts         # URL slug generation
│   ├── sanitize.ts        # String cleaning utilities
│   └── validators.ts      # String validation
└── array/
    ├── groupBy.ts         # Array grouping utility
    ├── chunk.ts           # Array chunking utility
    └── unique.ts          # Deduplication utility
```

## Code Examples

### Bacterial Utilities

```typescript
// utils/array/groupBy.ts
export function groupBy<T, K extends string | number>(
  array: T[],
  keyFn: (item: T) => K
): Record<K, T[]> {
  return array.reduce((groups, item) => {
    const key = keyFn(item);
    groups[key] = groups[key] || [];
    groups[key].push(item);
    return groups;
  }, {} as Record<K, T[]>);
}

// Usage: const grouped = groupBy(users, user => user.role);
```

```typescript
// utils/string/slugify.ts
export function slugify(text: string): string {
  return text
    .toLowerCase()
    .trim()
    .replace(/[^\w\s-]/g, '')
    .replace(/[\s_-]+/g, '-')
    .replace(/^-+|-+$/g, '');
}

// Usage: const slug = slugify("Hello World!"); // "hello-world"
```

```typescript
// utils/validation/email.ts
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// Usage: if (isValidEmail(userInput)) { ... }
```

### Bacterial Data Structures

```typescript
// utils/data/Result.ts
export class Result<T, E = Error> {
  private constructor(
    private readonly success: boolean,
    private readonly value?: T,
    private readonly error?: E
  ) {}

  static ok<T>(value: T): Result<T> {
    return new Result(true, value);
  }

  static err<E>(error: E): Result<never, E> {
    return new Result(false, undefined, error);
  }

  isOk(): boolean {
    return this.success;
  }

  isErr(): boolean {
    return !this.success;
  }

  unwrap(): T {
    if (!this.success) throw this.error;
    return this.value!;
  }

  unwrapOr(defaultValue: T): T {
    return this.success ? this.value! : defaultValue;
  }
}
```

## Anti-Patterns to Avoid

### 1. **The God Function**
```typescript
// ❌ BAD: Does too many things
function processUserData(userData: any) {
  // validates data
  // transforms data
  // saves to database
  // sends email
  // logs audit trail
  // updates cache
  // ... 200 lines later
}
```

### 2. **The Dependency Web**
```typescript
// ❌ BAD: Too many imports
import { ServiceA } from './serviceA';
import { ServiceB } from './serviceB';
import { ServiceC } from './serviceC';
import { UtilityX } from './utilityX';
import { HelperY } from './helperY';
// ... 15 more imports
```

### 3. **The Configuration Nightmare**
```typescript
// ❌ BAD: Requires complex setup
class DatabaseManager {
  constructor(config: {
    host: string;
    port: number;
    username: string;
    password: string;
    database: string;
    ssl: SSLConfig;
    pooling: PoolConfig;
    logging: LogConfig;
    // ... 20 more config options
  }) {}
}
```

## When to Go Eukaryotic

Sometimes you need the eukaryotic approach for complex, coordinated systems:

### Use Eukaryotic Patterns For:
- **Application Architecture**: Main app structure, routing, middleware
- **Business Logic Orchestration**: Complex workflows that coordinate multiple services
- **State Management**: Global application state that needs coordination
- **Infrastructure**: Database connections, authentication systems, logging

### The Hybrid Approach
```typescript
// Eukaryotic backbone
class OrderService {
  constructor(
    private paymentService: PaymentService,
    private inventoryService: InventoryService
  ) {}

  async processOrder(order: Order): Promise<Result<ProcessedOrder>> {
    // Use bacterial utilities within eukaryotic orchestration
    const validatedOrder = validateOrder(order); // bacterial function
    const formattedPrice = formatCurrency(order.total); // bacterial function
    const orderSlug = slugify(order.description); // bacterial function
    
    // Complex orchestration logic here...
    return Result.ok(processedOrder);
  }
}
```

## Tools and Techniques

### 1. **Pure Functions First**
Default to pure functions that don't depend on external state.

### 2. **Minimal Interfaces**
Keep function signatures simple and focused.

### 3. **Self-Documenting Code**
Write code so clear that comments are rarely needed.

### 4. **Single Responsibility**
Each function should do one thing well.

### 5. **Composition Over Inheritance**
Build complex behavior by combining simple functions.

---

## The Bacterial Programmer's Oath

*"I will write code that others can yoink with joy. My functions will be small, my modules will be independent, and my utilities will spread through the codebase like beneficial bacteria, making every developer's life a little bit easier."*

---

*Remember: The goal is not to avoid all complexity, but to isolate complexity in eukaryotic orchestration layers while maximizing the amount of simple, reusable bacterial code that can thrive through horizontal gene transfer.*
