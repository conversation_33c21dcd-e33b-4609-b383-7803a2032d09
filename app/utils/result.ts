// 🦠 BACTERIAL RESULT UTILITIES
// Small, pure, self-contained result handling functions

/**
 * Standard success result type.
 */
export type SuccessResult<T = any> = {
  success: true;
  data?: T;
  message?: string;
};

/**
 * Standard error result type.
 */
export type ErrorResult = {
  success: false;
  error: string;
  details?: string[];
};

/**
 * Union type for operation results.
 */
export type Result<T = any> = SuccessResult<T> | ErrorResult;

/**
 * Create a success result.
 * Usage: createSuccess('Operation completed', { id: 123 })
 */
export const createSuccess = <T = any>(
  message?: string,
  data?: T
): SuccessResult<T> => ({
  success: true,
  message,
  data
});

/**
 * Create an error result.
 * Usage: createError('Operation failed', ['Field is required'])
 */
export const createError = (
  error: string,
  details?: string[]
): ErrorResult => ({
  success: false,
  error,
  details
});

/**
 * Check if result is successful.
 * Usage: isSuccess(result) → boolean
 */
export const isSuccess = <T>(result: Result<T>): result is SuccessResult<T> => {
  return result.success === true;
};

/**
 * Check if result is an error.
 * Usage: isError(result) → boolean
 */
export const isError = <T>(result: Result<T>): result is ErrorResult => {
  return result.success === false;
};

/**
 * Transform a result's data while preserving error state.
 * Usage: mapResult(result, data => data.id) → Result<number>
 */
export const mapResult = <T, U>(
  result: Result<T>,
  transform: (data: T) => U
): Result<U> => {
  if (isError(result)) {
    return result;
  }
  
  return createSuccess(
    result.message,
    result.data ? transform(result.data) : undefined
  );
};

/**
 * Chain results together, stopping on first error.
 * Usage: chainResults([result1, result2], (results) => combineData(results))
 */
export const chainResults = <T, U>(
  results: Result<T>[],
  combiner: (data: T[]) => U
): Result<U> => {
  const errors: string[] = [];
  const data: T[] = [];
  
  for (const result of results) {
    if (isError(result)) {
      errors.push(result.error);
      if (result.details) {
        errors.push(...result.details);
      }
    } else if (result.data !== undefined) {
      data.push(result.data);
    }
  }
  
  if (errors.length > 0) {
    return createError('Multiple operations failed', errors);
  }
  
  return createSuccess('All operations completed', combiner(data));
};

/**
 * Convert a promise to a Result, catching errors.
 * Usage: await fromPromise(asyncOperation()) → Result<T>
 */
export const fromPromise = async <T>(
  promise: Promise<T>
): Promise<Result<T>> => {
  try {
    const data = await promise;
    return createSuccess(undefined, data);
  } catch (error) {
    const message = error instanceof Error ? error.message : String(error);
    return createError(message);
  }
};
