// 🦠 BACTERIAL GRAPHQL UTILITIES
// Small, pure, self-contained GraphQL helper functions

/**
 * Extract user errors from GraphQL response.
 * Usage: extractUserErrors(response, 'bulkOperationRunQuery') → string[] | null
 */
export const extractUserErrors = (
  responseData: any, 
  operationName: string
): string[] | null => {
  const userErrors = responseData?.data?.[operationName]?.userErrors;
  if (!userErrors || !Array.isArray(userErrors) || userErrors.length === 0) {
    return null;
  }
  return userErrors.map((error: any) => error.message || 'Unknown error');
};

/**
 * Extract operation result from GraphQL response.
 * Usage: extractOperationResult(response, 'bulkOperationRunQuery', 'bulkOperation')
 */
export const extractOperationResult = <T = any>(
  responseData: any,
  operationName: string,
  resultField: string
): T | null => {
  return responseData?.data?.[operationName]?.[resultField] || null;
};

/**
 * Create a standardized GraphQL error response.
 * Usage: createGraphQLError('Operation failed', ['Field is required'])
 */
export const createGraphQLError = (
  message: string,
  errors?: string[]
) => ({
  success: false as const,
  error: message,
  details: errors || []
});

/**
 * Create a standardized GraphQL success response.
 * Usage: createGraphQLSuccess('Operation completed', { id: '123' })
 */
export const createGraphQLSuccess = <T = any>(
  message: string,
  data?: T
) => ({
  success: true as const,
  message,
  data
});

/**
 * Check if GraphQL response has errors.
 * Usage: hasGraphQLErrors(response) → boolean
 */
export const hasGraphQLErrors = (responseData: any): boolean => {
  return !!(responseData?.errors && responseData.errors.length > 0);
};

/**
 * Extract GraphQL errors from response.
 * Usage: extractGraphQLErrors(response) → string[]
 */
export const extractGraphQLErrors = (responseData: any): string[] => {
  if (!hasGraphQLErrors(responseData)) return [];
  return responseData.errors.map((error: any) => error.message || 'Unknown GraphQL error');
};

/**
 * Validate GraphQL response structure.
 * Usage: validateGraphQLResponse(response, ['data', 'bulkOperationRunQuery'])
 */
export const validateGraphQLResponse = (
  responseData: any,
  requiredPath: string[]
): boolean => {
  return requiredPath.reduce((current, key) => {
    return current && typeof current === 'object' && key in current ? current[key] : null;
  }, responseData) !== null;
};

/**
 * Create bulk operation mutation query.
 * Usage: createBulkOperationMutation() → string
 */
export const createBulkOperationMutation = (): string => `#graphql
  mutation bulkOperationRunQuery($query: String!) {
    bulkOperationRunQuery(query: $query) {
      bulkOperation { id status }
      userErrors { field message }
    }
  }`;

/**
 * Parse JSON response safely.
 * Usage: parseGraphQLResponse(response) → Promise<any>
 */
export const parseGraphQLResponse = async (response: Response): Promise<any> => {
  try {
    return await response.json();
  } catch (error) {
    throw new Error(`Failed to parse GraphQL response: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};
