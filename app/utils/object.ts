// 🦠 BACTERIAL OBJECT UTILITIES
// Small, pure, self-contained object manipulation functions

/**
 * Enrich array of objects with additional property.
 * Usage: enrichWithProperty(users, user => user.isActive, 'status')
 */
export const enrichWithProperty = <T, K>(
  items: T[],
  enrichFn: (item: T) => K,
  propName: string
): (T & Record<string, K>)[] => {
  return items.map(item => ({
    ...item,
    [propName]: enrichFn(item)
  }));
};

/**
 * Pick specific properties from object.
 * Usage: pick({a: 1, b: 2, c: 3}, ['a', 'c']) → {a: 1, c: 3}
 */
export const pick = <T extends Record<string, any>, K extends keyof T>(
  obj: T,
  keys: K[]
): Pick<T, K> => {
  const result = {} as Pick<T, K>;
  keys.forEach(key => {
    if (key in obj) {
      result[key] = obj[key];
    }
  });
  return result;
};

/**
 * Omit specific properties from object.
 * Usage: omit({a: 1, b: 2, c: 3}, ['b']) → {a: 1, c: 3}
 */
export const omit = <T, K extends keyof T>(
  obj: T, 
  keys: K[]
): Omit<T, K> => {
  const result = { ...obj };
  keys.forEach(key => {
    delete result[key];
  });
  return result;
};

/**
 * Check if object has nested property safely.
 * Usage: hasNestedProperty({a: {b: {c: 1}}}, ['a', 'b', 'c']) → true
 */
export const hasNestedProperty = (obj: any, path: string[]): boolean => {
  return path.reduce((current, key) => {
    return current && typeof current === 'object' && key in current ? current[key] : undefined;
  }, obj) !== undefined;
};

/**
 * Get nested property value safely.
 * Usage: getNestedProperty({a: {b: {c: 1}}}, ['a', 'b', 'c']) → 1
 */
export const getNestedProperty = <T = any>(obj: any, path: string[]): T | undefined => {
  return path.reduce((current, key) => {
    return current && typeof current === 'object' && key in current ? current[key] : undefined;
  }, obj);
};

/**
 * Check if value is a plain object (not array, null, etc).
 * Usage: isPlainObject({}) → true, isPlainObject([]) → false
 */
export const isPlainObject = (value: any): value is Record<string, any> => {
  return value !== null && typeof value === 'object' && !Array.isArray(value);
};

/**
 * Deep merge two objects (simple version).
 * Usage: deepMerge({a: 1}, {b: 2}) → {a: 1, b: 2}
 */
export const deepMerge = <T extends Record<string, any>>(
  target: T,
  source: Partial<T>
): T => {
  const result = { ...target } as any;

  Object.keys(source).forEach(key => {
    const sourceValue = (source as any)[key];
    const targetValue = result[key];

    if (isPlainObject(sourceValue) && isPlainObject(targetValue)) {
      result[key] = deepMerge(targetValue, sourceValue);
    } else {
      result[key] = sourceValue;
    }
  });

  return result;
};
