// 🦠 BACTERIAL ARRAY UTILITIES
// Small, pure, self-contained functions that can be "yoinked" anywhere

/**
 * Generate a range of consecutive numbers.
 * Usage: range(1, 5) → [1, 2, 3, 4, 5]
 */
export const range = (start: number, end: number): number[] => {
  const length = end - start + 1;
  return Array.from({ length }, (_, idx) => idx + start);
};

/**
 * Chunk an array into smaller arrays of specified size.
 * Usage: chunk([1,2,3,4,5], 2) → [[1,2], [3,4], [5]]
 */
export const chunk = <T>(array: T[], size: number): T[][] => {
  const chunks: T[][] = [];
  for (let i = 0; i < array.length; i += size) {
    chunks.push(array.slice(i, i + size));
  }
  return chunks;
};

/**
 * Get unique values from an array.
 * Usage: unique([1,2,2,3,3,3]) → [1,2,3]
 */
export const unique = <T>(array: T[]): T[] => [...new Set(array)];

/**
 * Check if array is empty.
 * Usage: isEmpty([]) → true
 */
export const isEmpty = <T>(array: T[]): boolean => array.length === 0;

/**
 * Get first element of array safely.
 * Usage: first([1,2,3]) → 1, first([]) → undefined
 */
export const first = <T>(array: T[]): T | undefined => array[0];

/**
 * Get last element of array safely.
 * Usage: last([1,2,3]) → 3, last([]) → undefined
 */
export const last = <T>(array: T[]): T | undefined => array[array.length - 1];

/**
 * Insert element at specific index without mutating original array.
 * Usage: insertAt([1,2,4], 2, 3) → [1,2,3,4]
 */
export const insertAt = <T>(array: T[], index: number, item: T): T[] => [
  ...array.slice(0, index),
  item,
  ...array.slice(index)
];

/**
 * Remove element at specific index without mutating original array.
 * Usage: removeAt([1,2,3,4], 2) → [1,2,4]
 */
export const removeAt = <T>(array: T[], index: number): T[] => [
  ...array.slice(0, index),
  ...array.slice(index + 1)
];

/**
 * Create a Set from array using a key function.
 * Usage: createSetFromProperty([{id: 1}, {id: 2}], item => item.id) → Set([1, 2])
 */
export const createSetFromProperty = <T, K>(
  array: T[],
  keyFn: (item: T) => K
): Set<K> => new Set(array.map(keyFn));

/**
 * Group array items by a key function.
 * Usage: groupBy([{type: 'A', val: 1}, {type: 'A', val: 2}], item => item.type)
 */
export const groupBy = <T, K extends string | number>(
  array: T[],
  keyFn: (item: T) => K
): Record<K, T[]> => {
  return array.reduce((groups, item) => {
    const key = keyFn(item);
    groups[key] = groups[key] || [];
    groups[key].push(item);
    return groups;
  }, {} as Record<K, T[]>);
};

/**
 * Deep equality check for arrays of objects.
 * Usage: deepArrayEquals([{a: 1}], [{a: 1}]) → true
 */
export const deepArrayEquals = <T>(a: T[], b: T[]): boolean => {
  if (a.length !== b.length) return false;
  return a.every((item, index) => {
    const other = b[index];
    if (typeof item !== typeof other) return false;
    if (typeof item !== 'object' || item === null) return item === other;

    const itemKeys = Object.keys(item as any);
    const otherKeys = Object.keys(other as any);
    if (itemKeys.length !== otherKeys.length) return false;

    return itemKeys.every(key => (item as any)[key] === (other as any)[key]);
  });
};

/**
 * Add unique ID to array items.
 * Usage: addIds([{name: 'John'}], 'user') → [{id: 'user-0', name: 'John'}]
 */
export const addIds = <T>(
  items: T[],
  prefix: string = 'item'
): (T & { id: string })[] => {
  return items.map((item, index) => ({
    ...item,
    id: `${prefix}-${index}`
  }));
};

/**
 * Remove ID property from array items.
 * Usage: removeIds([{id: '1', name: 'John'}]) → [{name: 'John'}]
 */
export const removeIds = <T extends { id: any }>(
  items: T[]
): Omit<T, 'id'>[] => {
  return items.map(({ id, ...rest }) => rest);
};
