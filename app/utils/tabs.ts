// 🦠 BACTERIAL TABS UTILITIES
// Small, pure, self-contained tab navigation functions

/**
 * Tab configuration interface.
 */
export interface TabConfig {
  id: string;
  content: string;
  badge?: string;
  disabled?: boolean;
}

/**
 * Library tabs configuration.
 * Bacterial approach: centralized tab definitions.
 */
export const LIBRARY_TABS: TabConfig[] = [
  { id: 'all', content: 'All' },
  { id: 'products', content: 'Products' },
  { id: 'orders', content: 'Orders' },
  { id: 'customers', content: 'Customers' },
];

/**
 * Find tab index by ID.
 * Usage: findTabIndex(LIBRARY_TABS, 'orders') → 2
 */
export const findTabIndex = (tabs: TabConfig[], tabId: string): number => {
  const index = tabs.findIndex(tab => tab.id === tabId);
  return index === -1 ? 0 : index;
};

/**
 * Get tab by index.
 * Usage: getTabByIndex(LIBRARY_TABS, 2) → { id: 'orders', content: 'Orders' }
 */
export const getTabByIndex = (tabs: TabConfig[], index: number): TabConfig | null => {
  return tabs[index] || null;
};

/**
 * Get tab by ID.
 * Usage: getTabById(LIBRARY_TABS, 'orders') → { id: 'orders', content: 'Orders' }
 */
export const getTabById = (tabs: TabConfig[], tabId: string): TabConfig | null => {
  return tabs.find(tab => tab.id === tabId) || null;
};

/**
 * Check if tab ID is valid.
 * Usage: isValidTabId(LIBRARY_TABS, 'orders') → true
 */
export const isValidTabId = (tabs: TabConfig[], tabId: string): boolean => {
  return tabs.some(tab => tab.id === tabId);
};

/**
 * Get default tab ID.
 * Usage: getDefaultTabId(LIBRARY_TABS) → 'all'
 */
export const getDefaultTabId = (tabs: TabConfig[]): string => {
  return tabs[0]?.id || 'all';
};

/**
 * Normalize tab ID (ensure it's valid or return default).
 * Usage: normalizeTabId(LIBRARY_TABS, 'invalid') → 'all'
 */
export const normalizeTabId = (tabs: TabConfig[], tabId: string): string => {
  return isValidTabId(tabs, tabId) ? tabId : getDefaultTabId(tabs);
};

/**
 * Create tab change handler.
 * Usage: const handler = createTabChangeHandler(tabs, onTabChange)
 */
export const createTabChangeHandler = (
  tabs: TabConfig[],
  onTabChange: (tabId: string) => void
) => (index: number) => {
  const tab = getTabByIndex(tabs, index);
  if (tab) {
    onTabChange(tab.id);
  }
};

/**
 * Tab state interface.
 */
export interface TabState {
  selectedId: string;
  selectedIndex: number;
  isValid: boolean;
}

/**
 * Create tab state from ID.
 * Usage: createTabState(LIBRARY_TABS, 'orders') → TabState
 */
export const createTabState = (tabs: TabConfig[], selectedId: string): TabState => {
  const normalizedId = normalizeTabId(tabs, selectedId);
  const selectedIndex = findTabIndex(tabs, normalizedId);
  const isValid = isValidTabId(tabs, selectedId);

  return {
    selectedId: normalizedId,
    selectedIndex,
    isValid
  };
};

/**
 * Update tab state.
 * Usage: updateTabState(currentState, tabs, 'newTabId') → TabState
 */
export const updateTabState = (
  currentState: TabState,
  tabs: TabConfig[],
  newTabId: string
): TabState => {
  return createTabState(tabs, newTabId);
};

/**
 * Get next tab ID.
 * Usage: getNextTabId(LIBRARY_TABS, 'orders') → 'customers'
 */
export const getNextTabId = (tabs: TabConfig[], currentId: string): string => {
  const currentIndex = findTabIndex(tabs, currentId);
  const nextIndex = (currentIndex + 1) % tabs.length;
  return tabs[nextIndex].id;
};

/**
 * Get previous tab ID.
 * Usage: getPreviousTabId(LIBRARY_TABS, 'orders') → 'products'
 */
export const getPreviousTabId = (tabs: TabConfig[], currentId: string): string => {
  const currentIndex = findTabIndex(tabs, currentId);
  const previousIndex = currentIndex === 0 ? tabs.length - 1 : currentIndex - 1;
  return tabs[previousIndex].id;
};

/**
 * Filter tabs by condition.
 * Usage: filterTabs(LIBRARY_TABS, tab => !tab.disabled) → TabConfig[]
 */
export const filterTabs = (
  tabs: TabConfig[],
  predicate: (tab: TabConfig) => boolean
): TabConfig[] => {
  return tabs.filter(predicate);
};

/**
 * Get enabled tabs only.
 * Usage: getEnabledTabs(LIBRARY_TABS) → TabConfig[]
 */
export const getEnabledTabs = (tabs: TabConfig[]): TabConfig[] => {
  return filterTabs(tabs, tab => !tab.disabled);
};
