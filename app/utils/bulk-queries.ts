// 🦠 BACTERIAL BULK QUERY UTILITIES
// Small, pure, self-contained GraphQL query definitions

import { JobType } from "@prisma/client";

/**
 * GraphQL query for UTM tagging orders.
 * Bacterial approach: focused, single-purpose query.
 */
export const UTM_ORDERS_QUERY = `#graphql
  query {
    orders(first: 250) {
      edges {
        node {
          __typename
          id
          tags
          customerJourneySummary {
            moments(first: 250) {
              edges {
                node {
                  __typename
                  ... on CustomerVisit {
                    utmParameters { campaign content medium source term }
                  }
                }
              }
            }
          }
        }
      }
      pageInfo {
        hasNextPage
        endCursor
      }
    }
  }`;

/**
 * GraphQL query for order collection tagging.
 * Bacterial approach: focused, single-purpose query.
 */
export const ORDER_COLLECTION_QUERY = `#graphql
  query {
    orders {
      edges {
        node {
          __typename
          id
          tags
          lineItems(first: 50) {
            edges {
              node {
                __typename
                id
                product {
                  id
                  collections(first: 50) {
                    edges {
                      node {
                        __typename
                        id
                        legacyResourceId
                        title
                        handle
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }`;

/**
 * GraphQL query for customer vendor tagging.
 * Bacterial approach: focused, single-purpose query.
 */
export const CUSTOMER_VENDOR_QUERY = `#graphql
  query {
    orders(first: 250) {
      edges {
        node {
          __typename
          id
          customer {
            id
            tags
          }
          lineItems(first: 250) {
            edges {
              node {
                __typename
                vendor
              }
            }
          }
        }
      }
      pageInfo {
        hasNextPage
        endCursor
      }
    }
  }`;

/**
 * Map of JobTypes to their corresponding bulk queries.
 * Bacterial approach: simple lookup table using pure query constants.
 */
export const BULK_QUERIES: Partial<Record<JobType, string>> = {
  [JobType.AUTO_TAG_ORDERS_UTM]: UTM_ORDERS_QUERY,
  [JobType.ORDER_COLLECTION_TAG]: ORDER_COLLECTION_QUERY,
  [JobType.AUTO_TAG_CUSTOMER_BY_VENDOR]: CUSTOMER_VENDOR_QUERY,
};

/**
 * Get bulk query for a job type.
 * Usage: getBulkQuery(JobType.AUTO_TAG_ORDERS_UTM) → string | null
 */
export const getBulkQuery = (jobType: JobType): string | null => {
  return BULK_QUERIES[jobType] || null;
};

/**
 * Check if job type supports bulk operations.
 * Usage: supportsBulkOperation(JobType.AUTO_TAG_ORDERS_UTM) → true
 */
export const supportsBulkOperation = (jobType: JobType): boolean => {
  return jobType in BULK_QUERIES;
};
