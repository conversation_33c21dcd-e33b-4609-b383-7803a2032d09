// 🦠 BACTERIAL SETTINGS UTILITIES
// Small, pure, self-contained settings validation and management functions

/**
 * Default application settings.
 * Bacterial approach: centralized configuration that can be "yoinked".
 */
export const DEFAULT_SETTINGS = {
  stuckJobTimeout: 30, // minutes
  maxRetries: 3,
  batchSize: 100
} as const;

/**
 * Settings validation rules.
 * Bacterial approach: centralized validation configuration.
 */
export const SETTINGS_VALIDATION = {
  stuckJobTimeout: {
    min: 1,
    max: 1440, // 24 hours
    errorMessages: {
      required: "Timeout cannot be empty.",
      notNumber: "Timeout must be a number.",
      tooSmall: "Timeout must be at least 1 minute.",
      tooLarge: "Timeout cannot exceed 1440 minutes (24 hours)."
    }
  }
} as const;

/**
 * Application settings interface.
 */
export interface AppSettings {
  stuckJobTimeout: number;
}

/**
 * Validate timeout value.
 * Usage: validateTimeout("30") → { isValid: true }
 */
export const validateTimeout = (value: string): { isValid: boolean; error?: string } => {
  const rules = SETTINGS_VALIDATION.stuckJobTimeout;
  
  if (!value || value.trim() === '') {
    return { isValid: false, error: rules.errorMessages.required };
  }
  
  const numValue = parseInt(value, 10);
  
  if (isNaN(numValue)) {
    return { isValid: false, error: rules.errorMessages.notNumber };
  }
  
  if (numValue < rules.min) {
    return { isValid: false, error: rules.errorMessages.tooSmall };
  }
  
  if (numValue > rules.max) {
    return { isValid: false, error: rules.errorMessages.tooLarge };
  }
  
  return { isValid: true };
};

/**
 * Parse timeout value safely.
 * Usage: parseTimeout("30") → 30 | null
 */
export const parseTimeout = (value: string): number | null => {
  const validation = validateTimeout(value);
  if (!validation.isValid) {
    return null;
  }
  return parseInt(value, 10);
};

/**
 * Create settings object from form data.
 * Usage: createSettingsFromFormData(formData) → AppSettings
 */
export const createSettingsFromFormData = (formData: FormData): AppSettings | null => {
  const timeout = formData.get("timeout") as string;
  const parsedTimeout = parseTimeout(timeout);
  
  if (parsedTimeout === null) {
    return null;
  }
  
  return {
    stuckJobTimeout: parsedTimeout
  };
};

/**
 * Validate complete settings object.
 * Usage: validateSettings(settings) → { isValid: boolean, errors: Record<string, string> }
 */
export const validateSettings = (settings: Partial<AppSettings>): { 
  isValid: boolean; 
  errors: Record<string, string> 
} => {
  const errors: Record<string, string> = {};
  
  if (settings.stuckJobTimeout !== undefined) {
    const timeoutValidation = validateTimeout(settings.stuckJobTimeout.toString());
    if (!timeoutValidation.isValid && timeoutValidation.error) {
      errors.stuckJobTimeout = timeoutValidation.error;
    }
  }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
};

/**
 * Get default settings with overrides.
 * Usage: getDefaultSettings({ stuckJobTimeout: 60 }) → merged settings
 */
export const getDefaultSettings = (overrides: Partial<AppSettings> = {}): AppSettings => {
  return {
    ...DEFAULT_SETTINGS,
    ...overrides
  };
};

/**
 * Check if settings are different from defaults.
 * Usage: hasCustomSettings(settings) → boolean
 */
export const hasCustomSettings = (settings: AppSettings): boolean => {
  const defaults = getDefaultSettings();
  return Object.keys(settings).some(key => 
    settings[key as keyof AppSettings] !== defaults[key as keyof AppSettings]
  );
};

/**
 * Format timeout for display.
 * Usage: formatTimeout(30) → "30 minutes"
 */
export const formatTimeout = (minutes: number): string => {
  if (minutes === 1) return "1 minute";
  if (minutes < 60) return `${minutes} minutes`;
  
  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;
  
  if (remainingMinutes === 0) {
    return hours === 1 ? "1 hour" : `${hours} hours`;
  }
  
  return `${hours}h ${remainingMinutes}m`;
};
