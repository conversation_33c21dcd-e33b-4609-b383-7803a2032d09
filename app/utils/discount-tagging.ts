// 🦠 BACTERIAL DISCOUNT TAGGING UTILITIES
// Small, focused functions for discount code tagging
import { addOrderTags } from './shopify-orders';

/**
 * Discount tagging job data structure.
 */
export interface DiscountTaggingJobData {
  orderId: string;
  discountCodes: string[];
}

/**
 * Validate discount codes array.
 * Usage: const validation = validateDiscountCodes(discountCodes)
 */
export const validateDiscountCodes = (discountCodes: any): { isValid: boolean; error?: string } => {
  if (!Array.isArray(discountCodes)) {
    return { isValid: false, error: 'discountCodes must be an array' };
  }
  
  if (discountCodes.length === 0) {
    return { isValid: false, error: 'discountCodes array cannot be empty' };
  }
  
  // Check if all elements are strings
  const invalidCodes = discountCodes.filter(code => typeof code !== 'string' || !code.trim());
  if (invalidCodes.length > 0) {
    return { isValid: false, error: 'All discount codes must be non-empty strings' };
  }
  
  return { isValid: true };
};

/**
 * Clean discount codes by trimming whitespace.
 * Usage: const cleanCodes = cleanDiscountCodes(discountCodes)
 */
export const cleanDiscountCodes = (discountCodes: string[]): string[] =>
  discountCodes
    .map(code => code.trim())
    .filter(Boolean);

/**
 * Validate discount tagging job data.
 * Usage: const validation = validateDiscountTaggingJobData(jobData)
 */
export const validateDiscountTaggingJobData = (jobData: any): { isValid: boolean; error?: string; data?: DiscountTaggingJobData } => {
  if (!jobData.orderId) {
    return { isValid: false, error: 'Missing orderId' };
  }
  
  if (typeof jobData.orderId !== 'string' || !jobData.orderId.trim()) {
    return { isValid: false, error: 'orderId must be a non-empty string' };
  }
  
  const codesValidation = validateDiscountCodes(jobData.discountCodes);
  if (!codesValidation.isValid) {
    return { isValid: false, error: codesValidation.error };
  }
  
  return { 
    isValid: true, 
    data: {
      orderId: jobData.orderId.trim(),
      discountCodes: cleanDiscountCodes(jobData.discountCodes)
    }
  };
};

/**
 * Create discount tagging context for logging.
 * Usage: const context = createDiscountTaggingContext(orderId, discountCodes)
 */
export const createDiscountTaggingContext = (
  orderId: string,
  discountCodes: string[]
): Record<string, any> => ({
  orderId,
  discountCodes,
  codeCount: discountCodes.length
});

/**
 * Process discount tagging for an order.
 * Usage: const result = await processDiscountTagging(admin, orderId, discountCodes)
 */
export const processDiscountTagging = async (
  admin: any,
  orderId: string,
  discountCodes: string[]
): Promise<{ success: boolean; errors?: any[] }> => {
  const cleanCodes = cleanDiscountCodes(discountCodes);
  
  if (cleanCodes.length === 0) {
    return { success: true };
  }
  
  return addOrderTags(admin, orderId, cleanCodes);
};

/**
 * Remove duplicate discount codes while preserving order.
 * Usage: const uniqueCodes = removeDuplicateDiscountCodes(discountCodes)
 */
export const removeDuplicateDiscountCodes = (discountCodes: string[]): string[] => {
  const seen = new Set<string>();
  return discountCodes.filter(code => {
    const cleanCode = code.trim().toLowerCase();
    if (seen.has(cleanCode)) {
      return false;
    }
    seen.add(cleanCode);
    return true;
  });
};

/**
 * Validate order ID format.
 * Usage: if (isValidOrderId(orderId)) { ... }
 */
export const isValidOrderId = (orderId: string): boolean => {
  if (!orderId || typeof orderId !== 'string') return false;
  return orderId.startsWith('gid://shopify/Order/');
};

/**
 * Extract numeric order ID from GID.
 * Usage: const numericId = extractOrderNumericId('gid://shopify/Order/123')
 */
export const extractOrderNumericId = (orderId: string): string => {
  const parts = orderId.split('/');
  return parts[parts.length - 1];
};

/**
 * Build order GID from numeric ID.
 * Usage: const gid = buildOrderGid('123')
 */
export const buildOrderGid = (numericId: string): string => {
  return `gid://shopify/Order/${numericId}`;
};
