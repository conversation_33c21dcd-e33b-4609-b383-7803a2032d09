// 🦠 BACTERIAL FORM UTILITIES
// Small, pure, self-contained form handling functions

/**
 * Check if form field is empty (null, undefined, or empty string after trim).
 * Usage: isEmptyField("  ") → true
 */
export const isEmptyField = (value: any): boolean => {
  return value == null || (typeof value === 'string' && value.trim() === '');
};

/**
 * Check if all specified fields in an object are empty.
 * Usage: areAllFieldsEmpty({name: '', email: ''}, ['name', 'email']) → true
 */
export const areAllFieldsEmpty = <T>(
  obj: T, 
  fields: (keyof T)[]
): boolean => {
  return fields.every(field => isEmptyField(obj[field]));
};

/**
 * Check if any specified fields in an object are empty.
 * Usage: areAnyFieldsEmpty({name: 'John', email: ''}, ['name', 'email']) → true
 */
export const areAnyFieldsEmpty = <T>(
  obj: T, 
  fields: (keyof T)[]
): boolean => {
  return fields.some(field => isEmptyField(obj[field]));
};

/**
 * Update a specific item in an array by ID.
 * Usage: updateItemById(items, 'id-1', {name: 'Updated'}) → updated array
 */
export const updateItemById = <T extends { id: any }>(
  items: T[],
  id: T['id'],
  updates: Partial<Omit<T, 'id'>>
): T[] => {
  return items.map(item => 
    item.id === id ? { ...item, ...updates } : item
  );
};

/**
 * Remove item from array by ID.
 * Usage: removeItemById(items, 'id-1') → filtered array
 */
export const removeItemById = <T extends { id: any }>(
  items: T[],
  id: T['id']
): T[] => {
  return items.filter(item => item.id !== id);
};

/**
 * Add item to array with auto-generated ID.
 * Usage: addItemWithId(items, {name: 'New'}, 'item') → array with new item
 */
export const addItemWithId = <T>(
  items: (T & { id: string })[],
  newItem: T,
  idPrefix: string = 'item'
): (T & { id: string })[] => {
  const nextId = Math.max(
    0,
    ...items
      .map(item => parseInt(item.id.split('-').pop() || '0'))
      .filter(num => !isNaN(num))
  ) + 1;
  
  return [...items, { ...newItem, id: `${idPrefix}-${nextId}` }];
};

/**
 * Ensure array has at least one item (add default if empty).
 * Usage: ensureMinimumItems([], () => ({name: ''})) → [{id: 'item-0', name: ''}]
 */
export const ensureMinimumItems = <T>(
  items: (T & { id: string })[],
  createDefault: () => T,
  idPrefix: string = 'item'
): (T & { id: string })[] => {
  if (items.length === 0) {
    return [{ ...createDefault(), id: `${idPrefix}-0` }];
  }
  return items;
};

/**
 * Check if last item in array matches condition.
 * Usage: lastItemMatches(items, item => item.name === '') → boolean
 */
export const lastItemMatches = <T>(
  items: T[],
  predicate: (item: T) => boolean
): boolean => {
  if (items.length === 0) return false;
  return predicate(items[items.length - 1]);
};

/**
 * Validate form field with multiple validators.
 * Usage: validateField('email@test', [isRequired, isEmail]) → {valid: true}
 */
export const validateField = (
  value: any,
  validators: ((value: any) => string | null)[]
): { valid: boolean; error?: string } => {
  for (const validator of validators) {
    const error = validator(value);
    if (error) {
      return { valid: false, error };
    }
  }
  return { valid: true };
};

/**
 * Common validator: required field.
 * Usage: isRequired('') → 'This field is required'
 */
export const isRequired = (value: any): string | null => {
  return isEmptyField(value) ? 'This field is required' : null;
};

/**
 * Common validator: minimum length.
 * Usage: minLength(3)('ab') → 'Must be at least 3 characters'
 */
export const minLength = (min: number) => (value: string): string | null => {
  return value && value.length < min ? `Must be at least ${min} characters` : null;
};

/**
 * Common validator: maximum length.
 * Usage: maxLength(10)('very long text') → 'Must be at most 10 characters'
 */
export const maxLength = (max: number) => (value: string): string | null => {
  return value && value.length > max ? `Must be at most ${max} characters` : null;
};

/**
 * Create a checkbox change handler for object properties.
 * Usage: const handler = createCheckboxHandler(config, onConfigChange)
 */
export const createCheckboxHandler = <T extends Record<string, any>>(
  config: T,
  onConfigChange: (newConfig: T) => void
) => (key: keyof T, checked: boolean) => {
  onConfigChange({ ...config, [key]: checked });
};

/**
 * Configuration item for checkbox groups.
 */
export interface CheckboxConfig {
  key: string;
  label: string;
  helpText?: string;
}

/**
 * Generate checkbox configurations from a mapping.
 * Usage: generateCheckboxConfigs(UTM_PARAMETERS) → CheckboxConfig[]
 */
export const generateCheckboxConfigs = (
  parameterMap: Record<string, string>
): CheckboxConfig[] => {
  return Object.entries(parameterMap).map(([key, label]) => ({
    key,
    label
  }));
};

/**
 * Form field configuration for dynamic form generation.
 */
export interface FormFieldConfig {
  key: string;
  type: 'text' | 'checkbox' | 'select';
  label: string;
  helpText?: string;
  placeholder?: string;
  required?: boolean;
  options?: { label: string; value: string }[];
  validator?: (value: any) => string | null;
}

/**
 * Create a generic form change handler.
 * Usage: const handler = createFormHandler(config, onConfigChange)
 */
export const createFormHandler = <T extends Record<string, any>>(
  config: T,
  onConfigChange: (newConfig: T) => void
) => (key: keyof T, value: any) => {
  onConfigChange({ ...config, [key]: value });
};

/**
 * Validate form field using its configuration.
 * Usage: validateFormField(fieldConfig, value) → { valid: boolean, error?: string }
 */
export const validateFormField = (
  fieldConfig: FormFieldConfig,
  value: any
): { valid: boolean; error?: string } => {
  // Check required fields
  if (fieldConfig.required && isEmptyField(value)) {
    return { valid: false, error: `${fieldConfig.label} is required` };
  }

  // Run custom validator if provided
  if (fieldConfig.validator) {
    const error = fieldConfig.validator(value);
    if (error) {
      return { valid: false, error };
    }
  }

  return { valid: true };
};

/**
 * Validate entire form using field configurations.
 * Usage: validateForm(formConfig, values) → { valid: boolean, errors: Record<string, string> }
 */
export const validateForm = <T extends Record<string, any>>(
  formConfig: FormFieldConfig[],
  values: T
): { valid: boolean; errors: Record<string, string> } => {
  const errors: Record<string, string> = {};

  formConfig.forEach(fieldConfig => {
    const value = values[fieldConfig.key];
    const validation = validateFormField(fieldConfig, value);

    if (!validation.valid && validation.error) {
      errors[fieldConfig.key] = validation.error;
    }
  });

  return {
    valid: Object.keys(errors).length === 0,
    errors
  };
};

/**
 * Create a simple field change handler for single-field forms.
 * Usage: const handler = createFieldHandler(config, onConfigChange, 'fieldName')
 */
export const createFieldHandler = <T extends Record<string, any>>(
  config: T,
  onConfigChange: (newConfig: T) => void,
  fieldKey: keyof T
) => (value: any) => {
  onConfigChange({ ...config, [fieldKey]: value });
};
