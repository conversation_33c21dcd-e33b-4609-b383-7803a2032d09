// 🦠 BACTERIAL DATE UTILITIES
// Small, pure, self-contained date manipulation functions

/**
 * Time units in milliseconds for easy calculations.
 * Usage: Date.now() + TIME_UNITS.day → tomorrow
 */
export const TIME_UNITS = {
  second: 1000,
  minute: 60 * 1000,
  hour: 60 * 60 * 1000,
  day: 24 * 60 * 60 * 1000,
  week: 7 * 24 * 60 * 60 * 1000,
  month: 30 * 24 * 60 * 60 * 1000, // Approximate
  year: 365 * 24 * 60 * 60 * 1000, // Approximate
} as const;

/**
 * Relative time units for formatting.
 */
export const RELATIVE_TIME_UNITS: { unit: Intl.RelativeTimeFormatUnit; ms: number }[] = [
  { unit: "year", ms: TIME_UNITS.year },
  { unit: "month", ms: TIME_UNITS.month },
  { unit: "week", ms: TIME_UNITS.week },
  { unit: "day", ms: TIME_UNITS.day },
  { unit: "hour", ms: TIME_UNITS.hour },
  { unit: "minute", ms: TIME_UNITS.minute },
  { unit: "second", ms: TIME_UNITS.second },
];

/**
 * Convert string or Date to Date object safely.
 * Usage: toDate("2023-01-01") → Date object
 */
export const toDate = (dateInput: string | Date): Date =>
  typeof dateInput === 'string' ? new Date(dateInput) : dateInput;

/**
 * Check if date is in the past.
 * Usage: isPast(new Date('2020-01-01')) → true
 */
export const isPast = (date: Date | string): boolean =>
  toDate(date).getTime() < Date.now();

/**
 * Check if date is in the future.
 * Usage: isFuture(new Date('2030-01-01')) → true
 */
export const isFuture = (date: Date | string): boolean =>
  toDate(date).getTime() > Date.now();

/**
 * Check if date is today.
 * Usage: isToday(new Date()) → true
 */
export const isToday = (date: Date | string): boolean => {
  const target = toDate(date);
  const today = new Date();
  return target.toDateString() === today.toDateString();
};

/**
 * Get difference between two dates in milliseconds.
 * Usage: dateDiff(date1, date2) → milliseconds
 */
export const dateDiff = (date1: Date | string, date2: Date | string): number =>
  toDate(date1).getTime() - toDate(date2).getTime();

/**
 * Add time to a date.
 * Usage: addTime(new Date(), 1, 'day') → tomorrow
 */
export const addTime = (
  date: Date | string, 
  amount: number, 
  unit: keyof typeof TIME_UNITS
): Date => {
  const baseDate = toDate(date);
  return new Date(baseDate.getTime() + amount * TIME_UNITS[unit]);
};

/**
 * Format date as ISO string (YYYY-MM-DD).
 * Usage: formatISODate(new Date()) → "2023-12-25"
 */
export const formatISODate = (date: Date | string): string =>
  toDate(date).toISOString().split('T')[0];

/**
 * Format date as ISO timestamp.
 * Usage: formatISOTimestamp(new Date()) → "2023-12-25T10:30:00.000Z"
 */
export const formatISOTimestamp = (date: Date | string): string =>
  toDate(date).toISOString();

/**
 * Get start of day for given date.
 * Usage: startOfDay(new Date()) → Date at 00:00:00
 */
export const startOfDay = (date: Date | string): Date => {
  const d = toDate(date);
  return new Date(d.getFullYear(), d.getMonth(), d.getDate());
};

/**
 * Get end of day for given date.
 * Usage: endOfDay(new Date()) → Date at 23:59:59.999
 */
export const endOfDay = (date: Date | string): Date => {
  const d = toDate(date);
  return new Date(d.getFullYear(), d.getMonth(), d.getDate(), 23, 59, 59, 999);
};

/**
 * Calculate delay in milliseconds from value and unit.
 * Usage: calculateDelayMs(5, 'minutes') → 300000
 */
export const calculateDelayMs = (value: number, unit: string): number => {
  if (isNaN(value) || value <= 0) return 0;

  switch (unit) {
    case 'seconds':
      return value * TIME_UNITS.second;
    case 'minutes':
      return value * TIME_UNITS.minute;
    case 'hours':
      return value * TIME_UNITS.hour;
    case 'days':
      return value * TIME_UNITS.day;
    default:
      return 0;
  }
};

/**
 * Calculate scheduled time from delay configuration.
 * Usage: calculateScheduledTime({value: 5, unit: 'minutes'}) → Date 5 minutes from now
 */
export const calculateScheduledTime = (delayConfig: {
  value?: number | string;
  unit?: string;
  timestamp?: string | number;
}): Date | undefined => {
  // Handle timestamp-based delay
  if (delayConfig.timestamp) {
    const scheduledAt = new Date(delayConfig.timestamp);
    return isFuture(scheduledAt) ? scheduledAt : undefined;
  }

  // Handle value/unit-based delay
  if (delayConfig.value && delayConfig.unit) {
    const value = Number(delayConfig.value);
    const delayMs = calculateDelayMs(value, delayConfig.unit);

    if (delayMs > 0) {
      return new Date(Date.now() + delayMs);
    }
  }

  return undefined;
};
