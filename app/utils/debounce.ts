// 🦠 BACTERIAL DEBOUNCE UTILITIES
// Small, pure, self-contained debouncing functions

/**
 * Create a debounced function that delays execution.
 * Usage: const debouncedFn = debounce(originalFn, 300)
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  delay: number
): ((...args: Parameters<T>) => void) => {
  let timeoutId: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
};

/**
 * Create a debounced function with immediate execution option.
 * Usage: const debouncedFn = debounceImmediate(originalFn, 300, true)
 */
export const debounceImmediate = <T extends (...args: any[]) => any>(
  func: T,
  delay: number,
  immediate: boolean = false
): ((...args: Parameters<T>) => void) => {
  let timeoutId: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    const callNow = immediate && !timeoutId;
    
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => {
      timeoutId = null as any;
      if (!immediate) func(...args);
    }, delay);
    
    if (callNow) func(...args);
  };
};

/**
 * Debounce constants for common use cases.
 * Bacterial approach: centralized timing configuration.
 */
export const DEBOUNCE_DELAYS = {
  SEARCH: 300,        // 300ms for search input
  RESIZE: 100,        // 100ms for window resize
  SCROLL: 50,         // 50ms for scroll events
  API_CALL: 500,      // 500ms for API calls
  VALIDATION: 200,    // 200ms for form validation
  AUTOCOMPLETE: 150   // 150ms for autocomplete
} as const;

/**
 * Create a search debouncer with optimal delay.
 * Usage: const debouncedSearch = createSearchDebouncer(searchFn)
 */
export const createSearchDebouncer = <T extends (...args: any[]) => any>(
  searchFunction: T
) => debounce(searchFunction, DEBOUNCE_DELAYS.SEARCH);

/**
 * Create a validation debouncer with optimal delay.
 * Usage: const debouncedValidate = createValidationDebouncer(validateFn)
 */
export const createValidationDebouncer = <T extends (...args: any[]) => any>(
  validationFunction: T
) => debounce(validationFunction, DEBOUNCE_DELAYS.VALIDATION);

/**
 * Create an API call debouncer with optimal delay.
 * Usage: const debouncedApiCall = createApiDebouncer(apiFn)
 */
export const createApiDebouncer = <T extends (...args: any[]) => any>(
  apiFunction: T
) => debounce(apiFunction, DEBOUNCE_DELAYS.API_CALL);

// Note: React hook version would require React imports, so we'll use a non-React approach

/**
 * Simple debounced value tracker without React dependencies.
 * Usage: const tracker = createDebouncedTracker(300)
 */
export const createDebouncedTracker = <T>(delay: number) => {
  let currentValue: T;
  let timeoutId: NodeJS.Timeout;
  let callbacks: ((value: T) => void)[] = [];

  return {
    setValue: (value: T) => {
      currentValue = value;
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        callbacks.forEach(callback => callback(currentValue));
      }, delay);
    },
    
    onUpdate: (callback: (value: T) => void) => {
      callbacks.push(callback);
      return () => {
        callbacks = callbacks.filter(cb => cb !== callback);
      };
    },
    
    getCurrentValue: () => currentValue,
    
    cancel: () => {
      clearTimeout(timeoutId);
    }
  };
};

/**
 * Throttle function - limits execution to once per interval.
 * Usage: const throttledFn = throttle(originalFn, 100)
 */
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
};
