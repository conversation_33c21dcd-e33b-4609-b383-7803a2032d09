// This is a dummy module to prevent Vite from bundling Prisma client in the browser.
// But we still need to export the types for client-side components.

// Export the JobType enum values as constants to match Prisma's generated types
export const JobType = {
  COLLECTION_VISIBILITY_UPDATE: 'COLLECTION_VISIBILITY_UPDATE',
  AUTO_TAG_ORDERS_UTM: 'AUTO_TAG_ORDERS_UTM',
  ORDER_COLLECTION_TAG: 'ORDER_COLLECTION_TAG',
  ORDER_CART_ATTRIBUTE_TAG: 'ORDER_CART_ATTRIBUTE_TAG',
  AUTO_TAG_CUSTOMER_BY_ORDER_TAG: 'AUTO_TAG_CUSTOMER_BY_ORDER_TAG',
  AUTO_TAG_CUSTOMER_BY_VENDOR: 'AUTO_TAG_CUSTOMER_BY_VENDOR',
  COLLECTION_VISIBILITY_BULK_UPDATE: 'COLLECTION_VISIBILITY_BULK_UPDATE',
  STUCK_JOB_CLEANUP: 'STUCK_JOB_CLEANUP',
  ORDER_DISCOUNT_TAG: 'ORDER_DISCOUNT_TAG',
  CANCEL_HIGH_RISK_ORDER: 'CANCEL_HIGH_RISK_ORDER'
} as const;

// Export JobStatus enum values as constants
export const JobStatus = {
  pending: 'pending',
  processing: 'processing',
  completed: 'completed',
  failed: 'failed',
  retrying: 'retrying',
  canceled: 'canceled',
  scheduled: 'scheduled'
} as const;

// Export type definitions to match Prisma's generated types
export type JobType = typeof JobType[keyof typeof JobType];
export type JobStatus = typeof JobStatus[keyof typeof JobStatus];

// Export empty objects for other Prisma exports that might be imported
export const PrismaClient = class {};
export default {};
