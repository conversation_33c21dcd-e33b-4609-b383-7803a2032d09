// 🦠 BACTERIAL SHOPIFY CUSTOMER UTILITIES
// Small, focused functions for customer operations

import { executeMutation } from './shopify';
import { extractUserErrors } from './shopify-orders';
import { ADD_TAGS_MUTATION } from './shopify-queries';

/**
 * Add tags to customer.
 * Usage: const result = await addCustomerTags(admin, customerId, tags)
 */
export const addCustomerTags = async (
  admin: any,
  customerId: string,
  tags: string[]
): Promise<{ success: boolean; errors: any[] }> => {
  const cleanTags = tags.filter(tag => tag.trim().length > 0);
  
  if (cleanTags.length === 0) {
    return { success: true, errors: [] };
  }

  const response = await executeMutation(admin, ADD_TAGS_MUTATION, {
    id: customerId,
    tags: cleanTags
  });

  const errors = extractUserErrors(response.tagsAdd);
  return {
    success: errors.length === 0,
    errors
  };
};

/**
 * Check if customer exists.
 * Usage: if (await customerExists(admin, customerId)) { ... }
 */
export const customerExists = async (admin: any, customerId: string): Promise<boolean> => {
  try {
    const query = `#graphql
      query getCustomer($customerId: ID!) {
        customer(id: $customerId) {
          id
        }
      }`;
    
    const response = await admin.graphql(query, { variables: { customerId } });
    const data = await response.json();
    return Boolean(data.data?.customer?.id);
  } catch {
    return false;
  }
};

/**
 * Get customer basic info.
 * Usage: const customer = await getCustomerBasic(admin, customerId)
 */
export const getCustomerBasic = async (
  admin: any, 
  customerId: string
): Promise<{ id: string; tags: string[] } | null> => {
  try {
    const query = `#graphql
      query getCustomerBasic($customerId: ID!) {
        customer(id: $customerId) {
          id
          tags
        }
      }`;
    
    const response = await admin.graphql(query, { variables: { customerId } });
    const data = await response.json();
    return data.data?.customer || null;
  } catch {
    return null;
  }
};

/**
 * Validate customer ID format.
 * Usage: if (isValidCustomerId(customerId)) { ... }
 */
export const isValidCustomerId = (customerId: string): boolean => {
  if (!customerId || typeof customerId !== 'string') return false;
  return customerId.startsWith('gid://shopify/Customer/');
};

/**
 * Extract numeric customer ID from GID.
 * Usage: const numericId = extractCustomerNumericId('gid://shopify/Customer/123')
 */
export const extractCustomerNumericId = (customerId: string): string => {
  const parts = customerId.split('/');
  return parts[parts.length - 1];
};

/**
 * Build customer GID from numeric ID.
 * Usage: const gid = buildCustomerGid('123')
 */
export const buildCustomerGid = (numericId: string): string => {
  return `gid://shopify/Customer/${numericId}`;
};
