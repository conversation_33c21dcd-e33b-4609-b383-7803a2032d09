// 🦠 BACTERIAL UTILITIES - Small, self-contained, easily "yoinkable"

import { range } from './array';
import { clamp } from './math';

const DOTS = '...';

/**
 * Calculate left and right sibling page indices around current page.
 * Bacterial utility: small, focused, no side effects.
 */
export const calculateSiblings = (
  current: number,
  neighbours: number,
  total: number
) => ({
  left: clamp(current - neighbours, 1, total),
  right: clamp(current + neighbours, 1, total)
});

/**
 * Determine if dots should be shown based on gap size.
 * Bacterial function: single responsibility, easily testable.
 */
export const shouldShowDots = (siblingIndex: number, boundary: number): boolean =>
  siblingIndex > boundary;

/**
 * Calculate total blocks needed for pagination display.
 * Bacterial utility: pure calculation, no dependencies.
 */
export const calculateTotalBlocks = (neighbours: number): number =>
  neighbours * 2 + 3 + 2; // neighbours + first + last + current + 2*DOTS

/**
 * Generate left-side page range (when only right dots are shown).
 * Bacterial function: focused on one specific case.
 */
export const generateLeftRange = (neighbours: number): number[] => {
  const itemCount = 3 + 2 * neighbours;
  return range(1, itemCount);
};

/**
 * Generate right-side page range (when only left dots are shown).
 * Bacterial function: focused on one specific case.
 */
export const generateRightRange = (total: number, neighbours: number): number[] => {
  const itemCount = 3 + 2 * neighbours;
  return range(total - itemCount + 1, total);
};

/**
 * Generate middle page range (when both dots are shown).
 * Bacterial function: simple range generation.
 */
export const generateMiddleRange = (left: number, right: number): number[] =>
  range(left, right);

// 🧬 COMPOSED FUNCTION - Uses bacterial utilities above

/**
 * Generates a pagination range with ellipses.
 * Composed from smaller bacterial functions for maximum reusability.
 */
export const generatePaginationRange = (
  currentPage: number,
  totalPages: number,
  pageNeighbours: number = 1
): (number | typeof DOTS)[] => {
  const totalBlocks = calculateTotalBlocks(pageNeighbours);

  // Simple case: show all pages
  if (totalPages <= totalBlocks) {
    return range(1, totalPages);
  }

  const { left: leftSibling, right: rightSibling } = calculateSiblings(
    currentPage,
    pageNeighbours,
    totalPages
  );

  const showLeftDots = shouldShowDots(leftSibling, 2);
  const showRightDots = shouldShowDots(totalPages - 1, rightSibling);

  // Case 1: Only right dots
  if (!showLeftDots && showRightDots) {
    const leftRange = generateLeftRange(pageNeighbours);
    return [...leftRange, DOTS, totalPages];
  }

  // Case 2: Only left dots
  if (showLeftDots && !showRightDots) {
    const rightRange = generateRightRange(totalPages, pageNeighbours);
    return [1, DOTS, ...rightRange];
  }

  // Case 3: Both dots
  if (showLeftDots && showRightDots) {
    const middleRange = generateMiddleRange(leftSibling, rightSibling);
    return [1, DOTS, ...middleRange, DOTS, totalPages];
  }

  // Fallback: show all pages
  return range(1, totalPages);
};

/**
 * Check if pagination should be shown.
 * Bacterial utility: simple boolean check.
 */
export const shouldShowPagination = (totalPages: number): boolean => totalPages > 1;

/**
 * Check if page is current page.
 * Bacterial utility: simple comparison.
 */
export const isCurrentPage = (page: number, currentPage: number): boolean => page === currentPage;

/**
 * Check if previous button should be disabled.
 * Bacterial utility: simple boundary check.
 */
export const isPreviousDisabled = (currentPage: number): boolean => currentPage === 1;

/**
 * Check if next button should be disabled.
 * Bacterial utility: simple boundary check.
 */
export const isNextDisabled = (currentPage: number, totalPages: number): boolean =>
  currentPage === totalPages;

/**
 * Get previous page number safely.
 * Bacterial utility: safe navigation.
 */
export const getPreviousPage = (currentPage: number): number => Math.max(1, currentPage - 1);

/**
 * Get next page number safely.
 * Bacterial utility: safe navigation.
 */
export const getNextPage = (currentPage: number, totalPages: number): number =>
  Math.min(totalPages, currentPage + 1);

/**
 * Check if item is dots separator.
 * Bacterial utility: type guard.
 */
export const isDots = (item: number | string): item is typeof DOTS => item === DOTS;

/**
 * Pagination constants for consistent styling.
 * Bacterial approach: centralized configuration.
 */
export const PAGINATION_CONSTANTS = {
  DOTS,
  DEFAULT_NEIGHBOURS: 1,
  DOT_SPACING: '0 var(--p-space-100)',
  BUTTON_GAP: '100'
} as const;