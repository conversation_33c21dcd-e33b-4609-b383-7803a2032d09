// 🦠 BACTERIAL SEARCH UTILITIES
// Small, pure, self-contained search and filtering functions

/**
 * Search configuration interface.
 */
export interface SearchConfig {
  query?: string;
  category?: string;
  page?: number;
  pageSize?: number;
}

/**
 * Default search configuration.
 * Bacterial approach: centralized defaults.
 */
export const DEFAULT_SEARCH_CONFIG: Required<SearchConfig> = {
  query: "",
  category: "all",
  page: 1,
  pageSize: 9
};

/**
 * Extract search parameters from URL.
 * Usage: extractSearchParams(url) → SearchConfig
 */
export const extractSearchParams = (url: URL): SearchConfig => {
  return {
    query: url.searchParams.get("query")?.toLowerCase() || DEFAULT_SEARCH_CONFIG.query,
    category: url.searchParams.get("category") || DEFAULT_SEARCH_CONFIG.category,
    page: parseInt(url.searchParams.get("page") || "1", 10),
    pageSize: DEFAULT_SEARCH_CONFIG.pageSize
  };
};

/**
 * Create search parameters for URL.
 * Usage: createSearchParams(config) → URLSearchParams
 */
export const createSearchParams = (config: Partial<SearchConfig>): URLSearchParams => {
  const params = new URLSearchParams();
  
  if (config.query && config.query.trim()) {
    params.set("query", config.query);
  }
  
  if (config.category && config.category !== "all") {
    params.set("category", config.category);
  }
  
  if (config.page && config.page > 1) {
    params.set("page", config.page.toString());
  }
  
  return params;
};

/**
 * Create FormData for search submission.
 * Usage: createSearchFormData(config) → FormData
 */
export const createSearchFormData = (config: Partial<SearchConfig>): FormData => {
  const formData = new FormData();
  
  formData.set("query", config.query || "");
  formData.set("category", config.category || "all");
  formData.set("page", (config.page || 1).toString());
  
  return formData;
};

/**
 * Calculate pagination info.
 * Usage: calculatePagination(totalItems, pageSize, currentPage) → PaginationInfo
 */
export interface PaginationInfo {
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  startIndex: number;
  endIndex: number;
}

export const calculatePagination = (
  totalItems: number, 
  pageSize: number, 
  currentPage: number
): PaginationInfo => {
  const totalPages = Math.ceil(totalItems / pageSize);
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = Math.min(startIndex + pageSize, totalItems);
  
  return {
    totalPages,
    hasNextPage: currentPage < totalPages,
    hasPreviousPage: currentPage > 1,
    startIndex,
    endIndex
  };
};

/**
 * Check if search has active filters.
 * Usage: hasActiveFilters(config) → boolean
 */
export const hasActiveFilters = (config: SearchConfig): boolean => {
  return !!(
    (config.query && config.query.trim()) ||
    (config.category && config.category !== "all")
  );
};

/**
 * Reset search to defaults.
 * Usage: resetSearch() → SearchConfig
 */
export const resetSearch = (): SearchConfig => ({ ...DEFAULT_SEARCH_CONFIG });

/**
 * Update search config with new values.
 * Usage: updateSearchConfig(currentConfig, { query: "new query" }) → SearchConfig
 */
export const updateSearchConfig = (
  current: SearchConfig, 
  updates: Partial<SearchConfig>
): SearchConfig => {
  return { ...current, ...updates };
};

/**
 * Validate page number.
 * Usage: validatePage(5, 10) → 5 (valid)
 */
export const validatePage = (page: number, totalPages: number): number => {
  if (page < 1) return 1;
  if (page > totalPages) return Math.max(1, totalPages);
  return page;
};

/**
 * Create search result summary.
 * Usage: createSearchSummary(config, totalItems) → string
 */
export const createSearchSummary = (config: SearchConfig, totalItems: number): string => {
  const parts: string[] = [];
  
  if (config.query && config.query.trim()) {
    parts.push(`"${config.query}"`);
  }
  
  if (config.category && config.category !== "all") {
    parts.push(`in ${config.category}`);
  }
  
  const searchText = parts.length > 0 ? ` for ${parts.join(" ")}` : "";
  const itemText = totalItems === 1 ? "result" : "results";
  
  return `${totalItems} ${itemText}${searchText}`;
};
