// 🦠 BACTERIAL TOAST UTILITIES
// Small, pure, self-contained toast notification management functions

/**
 * Toast state interface.
 */
export interface ToastState {
  content: string;
  isError: boolean;
  isActive: boolean;
}

/**
 * Default toast state.
 * Bacterial approach: centralized default state.
 */
export const DEFAULT_TOAST_STATE: ToastState = {
  content: "",
  isError: false,
  isActive: false
};

/**
 * Create success toast state.
 * Usage: createSuccessToast("Settings saved!") → ToastState
 */
export const createSuccessToast = (content: string): ToastState => ({
  content,
  isError: false,
  isActive: true
});

/**
 * Create error toast state.
 * Usage: createErrorToast("Something went wrong") → ToastState
 */
export const createErrorToast = (content: string): ToastState => ({
  content,
  isError: true,
  isActive: true
});

/**
 * Create inactive toast state.
 * Usage: createInactiveToast() → ToastState
 */
export const createInactiveToast = (): ToastState => ({
  ...DEFAULT_TOAST_STATE,
  isActive: false
});

/**
 * Check if toast should be shown.
 * Usage: shouldShowToast(toastState) → boolean
 */
export const shouldShowToast = (toast: ToastState): boolean => 
  toast.isActive && toast.content.trim().length > 0;

/**
 * Toast action creators for common scenarios.
 * Bacterial approach: predefined actions for common use cases.
 */
export const TOAST_ACTIONS = {
  settingsSaved: () => createSuccessToast("Settings saved successfully!"),
  settingsError: (error: string) => createErrorToast(error),
  validationError: () => createErrorToast("Please fix the errors before saving."),
  networkError: () => createErrorToast("Network error. Please try again."),
  unexpectedError: () => createErrorToast("An unexpected error occurred."),
  dismiss: () => createInactiveToast()
} as const;

/**
 * Extract toast state from fetcher response.
 * Usage: extractToastFromResponse(fetcherData) → ToastState
 */
export const extractToastFromResponse = (data: any): ToastState => {
  if (!data) {
    return DEFAULT_TOAST_STATE;
  }
  
  if ('error' in data && data.error) {
    return createErrorToast(data.error);
  }
  
  if ('ok' in data && data.ok) {
    return createSuccessToast("Settings saved successfully!");
  }
  
  return DEFAULT_TOAST_STATE;
};

/**
 * Create toast handler functions.
 * Usage: const { showSuccess, showError, dismiss } = createToastHandlers(setToast)
 */
export const createToastHandlers = (setToast: (toast: ToastState) => void) => ({
  showSuccess: (content: string) => setToast(createSuccessToast(content)),
  showError: (content: string) => setToast(createErrorToast(content)),
  dismiss: () => setToast(createInactiveToast()),
  fromResponse: (data: any) => setToast(extractToastFromResponse(data))
});

/**
 * Toast duration constants.
 * Bacterial approach: centralized timing configuration.
 */
export const TOAST_DURATION = {
  SUCCESS: 3000, // 3 seconds
  ERROR: 5000,   // 5 seconds
  WARNING: 4000  // 4 seconds
} as const;

/**
 * Auto-dismiss toast after specified duration.
 * Usage: autoHideToast(toastState, dismissFn, TOAST_DURATION.SUCCESS)
 */
export const autoHideToast = (
  toast: ToastState, 
  dismissFn: () => void, 
  duration: number = TOAST_DURATION.SUCCESS
): NodeJS.Timeout | null => {
  if (!shouldShowToast(toast)) {
    return null;
  }
  
  return setTimeout(dismissFn, duration);
};
