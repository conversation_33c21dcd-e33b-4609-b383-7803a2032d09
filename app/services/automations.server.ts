import prisma from "../db.server";
import { JobStatus, type JobType } from "@prisma/client";
import { addJob } from "../queues/jobQueue.server";
import { createSetFromProperty } from "../utils/array";
import { enrichWithProperty, isPlainObject } from "../utils/object";
import { calculateScheduledTime } from "../utils/date";
import { validateDelayConfig } from "../utils/validation";

/**
 * Seeds automation records for a given shop if they don't already exist.
 * This ensures that when new task templates are added to the library,
 * they appear for all existing users as "Inactive".
 * This function is now private to the module and called by the main export.
 *
 * @param shop The shop domain.
 */
async function seedAutomationsForShop(shop: string) {
  const allTaskTemplates = await prisma.taskTemplate.findMany();
  const existingShopAutomations = await prisma.automation.findMany({
    where: { shop },
    select: { type: true },
  });
  const existingShopAutomationTypes = createSetFromProperty(existingShopAutomations, a => a.type);

  for (const template of allTaskTemplates) {
    if (!existingShopAutomationTypes.has(template.type)) {
      try {
        await prisma.automation.create({
          data: {
            shop: shop,
            name: template.title,
            type: template.type,
            trigger: template.trigger,
            status: "Inactive",
          }
        });
        console.log(`Seeded automation ${template.type} for shop ${shop}`);
      } catch (error) {
        // Log error but continue, so one failure doesn't stop others.
        // This can happen in a race condition but is safe to ignore.
        console.error(`Failed to seed automation ${template.type} for shop ${shop}:`, error);
      }
    }
  }
}

/**
 * Fetches all automations for a shop and enriches them with their current
 * running status by checking for processing jobs.
 *
 * @param shop The shop domain.
 * @returns A list of automations with an added 'running' boolean property.
 */
export async function getAutomationsWithStatus(shop: string) {
  await seedAutomationsForShop(shop);

  // Fetch all automations for the shop.
  const automations = await prisma.automation.findMany({
    where: { shop },
    orderBy: { createdAt: 'asc' },
    select: { // Explicitly select all fields needed
      id: true,
      name: true,
      type: true,
      trigger: true,
      status: true,
      config: true,
      createdAt: true,
      updatedAt: true,
      lastRunAt: true, // Ensure lastRunAt is selected
      shop: true,
    },
  });

  // Find any jobs of these types currently being processed for this shop.
  const automationTypes = automations.map(a => a.type);
  const processingJobs = await prisma.job.findMany({
    where: {
      shop,
      status: JobStatus.processing,
      type: { in: automationTypes },
    },
    select: { type: true },
  });
  const processingTypes = createSetFromProperty(processingJobs, job => job.type);

  // Combine the data to include a 'running' status using bacterial enrichment.
  const result = enrichWithProperty(
    automations,
    automation => processingTypes.has(automation.type),
    'running'
  );

  return result
}

/**
 * Calculates the scheduled timestamp for a job based on the automation's delay configuration.
 * Now uses bacterial utilities for cleaner, more testable code.
 * @param shop The shop domain.
 * @param jobType The type of job to check for delay configuration.
 * @returns A Date object if a valid delay is configured, otherwise undefined.
 */
export async function calculateScheduledAt(shop: string, jobType: JobType): Promise<Date | undefined> {
  // Fetch the automation configuration to check for delay settings
  const automation = await prisma.automation.findUnique({
    where: {
      shop_type: {
        shop: shop,
        type: jobType
      }
    }
  });

  // Use bacterial utilities to validate and calculate scheduled time
  if (automation && isPlainObject(automation.config) && 'delay' in automation.config) {
    const delayConfig = automation.config.delay;

    if (validateDelayConfig(delayConfig)) {
      return calculateScheduledTime(delayConfig as any);
    } else {
      console.warn(`Invalid delay configuration for ${jobType}:`, delayConfig);
    }
  }

  return undefined;
}

/**
 * Creates a job for a specific automation, applying any delay specified in the automation config.
 * @param shop The shop domain.
 * @param jobType The type of job to create.
 * @param data The data payload for the job.
 * @param sessionId Optional session ID for the job.
 * @returns The created job object.
 */
export async function createAutomationJob(shop: string, jobType: JobType, data: any, sessionId?: string) {
  const scheduledAt = await calculateScheduledAt(shop, jobType);
  // Create the job with the calculated scheduledAt if applicable
  return await addJob({
    shop,
    jobType,
    data,
    sessionId,
    scheduledAt
  });
}
