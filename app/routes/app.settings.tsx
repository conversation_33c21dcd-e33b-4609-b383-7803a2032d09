import { type ActionFunctionArgs, type LoaderFunctionArgs } from "@remix-run/node";
import { useFetcher, useLoaderData } from "@remix-run/react";
import { useState, useEffect } from "react";
import {
  Card,
  Layout,
  Page,
  TextField,
  Button,
  BlockStack,
  Toast,
} from "@shopify/polaris";
import { authenticate } from "../shopify.server";
import prisma from "../db.server";
import {
  validateTimeout,
  createSettingsFromFormData,
  DEFAULT_SETTINGS
} from "../utils/settings";
import {
  type ToastState,
  DEFAULT_TOAST_STATE,
  extractToastFromResponse,
  shouldShowToast
} from "../utils/toast";

// LOADER
export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { session } = await authenticate.admin(request);
  const appSettings = await prisma.appSetting.findUnique({
    where: { shop: session.shop },
  });
  return new Response(JSON.stringify({ settings: appSettings?.settings }), {
    headers: { "Content-Type": "application/json" }
  });
};

// ACTION
export const action = async ({ request }: ActionFunctionArgs) => {
  const { session } = await authenticate.admin(request);
  const formData = await request.formData();

  // Use bacterial utility to create and validate settings
  const settings = createSettingsFromFormData(formData);

  if (!settings) {
    const timeout = formData.get("timeout") as string;
    const validation = validateTimeout(timeout);
    return new Response(JSON.stringify({
      error: validation.error || "Invalid settings provided."
    }), { status: 400, headers: { "Content-Type": "application/json" } });
  }

  await prisma.appSetting.upsert({
    where: { shop: session.shop },
    update: { settings: settings as any },
    create: {
      shop: session.shop,
      settings: settings as any,
    },
  });

  return new Response(JSON.stringify({ ok: true }), { headers: { "Content-Type": "application/json" } });
};

export default function Settings() {
  const { settings } = useLoaderData<{ settings: { stuckJobTimeout: number } }>();
  const fetcher = useFetcher<typeof action>();
  const [timeout, setTimeout] = useState(
    settings?.stuckJobTimeout?.toString() || DEFAULT_SETTINGS.stuckJobTimeout.toString()
  );
  const [timeoutError, setTimeoutError] = useState<string | undefined>(undefined);

  // Use bacterial utility for toast state management
  const [toast, setToast] = useState<ToastState>(DEFAULT_TOAST_STATE);

  const handleToastDismiss = () => setToast(DEFAULT_TOAST_STATE);

  useEffect(() => {
    if (fetcher.state === 'idle' && fetcher.data) {
      // Use bacterial utility to extract toast state from response
      const newToast = extractToastFromResponse(fetcher.data);
      setToast(newToast);
    }
  }, [fetcher.data, fetcher.state]);

  const handleTimeoutChange = (value: string) => {
    setTimeout(value);
    // Use bacterial utility for validation
    const validation = validateTimeout(value);
    setTimeoutError(validation.isValid ? undefined : validation.error);
  };

  return (
    <Page title="Settings">
      <Layout>
        <Layout.Section>
          <Card>
            <BlockStack gap="400">
              <fetcher.Form method="post">
                <TextField
                  label="Stuck Job Timeout (minutes)"
                  type="number"
                  name="timeout"
                  value={timeout}
                  onChange={handleTimeoutChange}
                  autoComplete="off"
                  error={timeoutError}
                  helpText="This setting determines how long a job can be in 'processing' status before it's automatically marked as 'failed'. A reasonable value prevents jobs from being stuck indefinitely."
                />
                <Button submit loading={fetcher.state !== 'idle'} disabled={fetcher.state !== 'idle' || !!timeoutError}>Save</Button>
              </fetcher.Form>
            </BlockStack>
          </Card>
        </Layout.Section>
      </Layout>
      {shouldShowToast(toast) && (
        <Toast content={toast.content} error={toast.isError} onDismiss={handleToastDismiss} />
      )}
    </Page>
  );
}
