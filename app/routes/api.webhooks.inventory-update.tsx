import type { ActionFunctionArgs } from "@remix-run/node";
import { JobType } from "@prisma/client";
import { authenticate } from "../shopify.server";
import { createAutomationJob } from "../services/automations.server";
import { logToFile } from "../utils/logger.server";
import prisma from "../db.server";

export const action = async ({ request }: ActionFunctionArgs) => {
  const { topic, shop, admin, payload } = await authenticate.webhook(
    request
  );

  if (!admin) {
    // The user hasn't installed the app yet, so we can't do anything.
    throw new Response("Unauthorized", { status: 401 });
  }

  switch (topic) {
    case "INVENTORY_LEVELS_UPDATE":
      const automation = await prisma.automation.findFirst({
        where: {
          type: JobType.COLLECTION_VISIBILITY_UPDATE,
          shop: shop,
        },
      });

      if (!automation || automation.status !== "Active") {
        logToFile("webhook", "info", `Skipping job for shop ${shop}, automation is not active.`);
        return new Response(JSON.stringify({ message: "Automation not active" }), {
          status: 200,
          headers: { "Content-Type": "application/json" }
        });
      }
      logToFile("webhook", "info", `Received INVENTORY_LEVELS_UPDATE webhook for shop: ${shop}`);
      try {
        // Cast to a more complete type based on the docs
        const inventoryPayload = payload as {
          inventory_item_id: number;
          available: number | null;
        };

        const { inventory_item_id: inventoryItemId, available } = inventoryPayload;

        if (!inventoryItemId) {
          logToFile("webhook", "error", `Missing inventory_item_id in webhook payload for shop: ${shop}`);
          return new Response(JSON.stringify({ message: "Missing inventory_item_id" }), {
            status: 400,
            headers: { "Content-Type": "application/json" }
          });
        }

        await createAutomationJob(shop, JobType.COLLECTION_VISIBILITY_UPDATE, { inventoryItemId, available });

        logToFile("webhook", "info", `Successfully enqueued COLLECTION_VISIBILITY_UPDATE job for inventory_item_id: ${inventoryItemId} for shop: ${shop}`);
        return new Response(JSON.stringify({ message: "Job enqueued successfully" }), {
          status: 200,
          headers: { "Content-Type": "application/json" }
        });
      } catch (error) {
        logToFile("webhook", "error", `Failed to enqueue job for INVENTORY_LEVELS_UPDATE webhook for shop: ${shop}: ${error}`);
        return new Response(JSON.stringify({ message: "Failed to enqueue job" }), {
          status: 500,
          headers: { "Content-Type": "application/json" }
        });
      }
    default:
      logToFile("webhook", "warn", `Unhandled webhook topic: ${topic} for shop: ${shop}`);
      throw new Response("Unhandled webhook topic", { status: 404 });
  }
};
