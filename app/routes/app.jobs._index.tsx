import { type ActionFunctionArgs, type LoaderFunctionArgs } from "@remix-run/node";
import { useFetcher, useLoaderData, useSubmit, Link as RemixLink } from "@remix-run/react";
import { useState, useEffect } from "react";
import { BlockStack, Layout, Page, Toast } from "@shopify/polaris";
import { JobStatus, JobType, type Job, type Prisma } from "@prisma/client";
import { authenticate } from "../shopify.server";
import prisma from "../db.server";
import { addJob } from "../queues/jobQueue.server";
import { NumberedPagination } from "../components/NumberedPagination";
import { usePollingWithFeedback } from "app/hooks/usePollingWithFeedback";
import { JobGrid, EmptyJobs, formatJobName } from "../components/JobCard";
import { JobFilters, JobFilterSummary, getStatusCounts } from "../components/JobFilters";

const PAGE_SIZE = 10; // Number of jobs per page

type LoaderData = {
  jobs: Job[];
  currentPage: number;
  totalPages: number;
  searchQuery: string;
  statusFilter: string;
  availableStatuses: JobStatus[];
};

type JobWithDates = Omit<Job, 'createdAt' | 'updatedAt' | 'completedAt' | 'startedAt' | 'scheduledAt' | 'lockedAt'> & {
  createdAt: Date;
  updatedAt: Date;
  completedAt: Date | null;
  startedAt: Date | null;
  scheduledAt: Date | null;
  lockedAt: Date | null;
};

// LOADER
export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { session } = await authenticate.admin(request);
  const url = new URL(request.url);
  const searchQuery = url.searchParams.get("query")?.trim() || "";
  const statusFilter = url.searchParams.get("status") || "all";
  const page = parseInt(url.searchParams.get("page") || "1", 10);

  type JobWhereInput = Prisma.JobWhereInput;
  const whereConditions: JobWhereInput[] = [{ shop: session.shop }];

  if (searchQuery) {
    const matchingJobTypes = Object.values(JobType).filter(jt =>
      formatJobName(jt).toLowerCase().includes(searchQuery.toLowerCase())
    );
    whereConditions.push({ type: { in: matchingJobTypes } });
  }

  if (statusFilter !== "all") {
    if (Object.values(JobStatus).includes(statusFilter as JobStatus)) {
      whereConditions.push({ status: statusFilter as JobStatus });
    }
  }

  const whereClause = { AND: whereConditions.length > 1 ? whereConditions : whereConditions[0] };

  const [jobs, totalJobs] = await prisma.$transaction([
    prisma.job.findMany({
      where: whereClause,
      orderBy: { createdAt: "desc" },
      skip: (page - 1) * PAGE_SIZE,
      take: PAGE_SIZE,
    }),
    prisma.job.count({ where: whereClause }),
  ]);

  const totalPages = Math.ceil(totalJobs / PAGE_SIZE);

  return new Response(JSON.stringify({
    jobs,
    currentPage: page,
    totalPages,
    searchQuery,
    statusFilter,
    availableStatuses: Object.values(JobStatus),
  }), {
    headers: { "Content-Type": "application/json" }
  });
};

// ACTION
export const action = async ({ request }: ActionFunctionArgs) => {
  await authenticate.admin(request);
  const formData = await request.formData();
  const { _action, id } = Object.fromEntries(formData);

  const jobId = id as string;

  switch (_action) {
    case "retryJob":
      const jobToRetry = await prisma.job.findUnique({ where: { id: jobId } });
      if (jobToRetry) {
        await addJob({
          shop: jobToRetry.shop,
          jobType: jobToRetry.type,
          data: JSON.parse(jobToRetry.data),
        });
        return new Response(JSON.stringify({ ok: true, message: "Job has been re-queued successfully." }), {
          headers: { "Content-Type": "application/json" },
        });
      }
      return new Response(JSON.stringify({ error: "Job not found." }), {
        status: 404,
        headers: { "Content-Type": "application/json" },
      });

    case "cancelJob":
      await prisma.job.update({
        where: { id: jobId },
        data: { status: "canceled", errorMessage: "Job canceled by user." },
      });
      return new Response(JSON.stringify({ ok: true, message: "Job has been canceled successfully." }), {
        headers: { "Content-Type": "application/json" },
      });

    default:
      return new Response(JSON.stringify({ error: "Invalid action" }), {
        status: 400,
        headers: { "Content-Type": "application/json" },
      });
  }
};

export default function Jobs() {
  const { jobs: serializedJobs, currentPage, totalPages, searchQuery, statusFilter, availableStatuses } = useLoaderData<LoaderData>();
  const fetcher = useFetcher<typeof action>();
  const submit = useSubmit();

  // 🦠 BACTERIAL STATE MANAGEMENT - Clean separation
  const [queryValue, setQueryValue] = useState(searchQuery);
  const [currentStatusFilter, setCurrentStatusFilter] = useState(statusFilter);
  const [toastContent, setToastContent] = useState("");
  const [isToastError, setIsToastError] = useState(false);
  const [toastActive, setToastActive] = useState(false);

  // 🦠 BACTERIAL POLLING - Auto-refresh functionality
  const { formattedTime, isFlashing } = usePollingWithFeedback(30000);

  const handleToastDismiss = () => setToastActive(false);

  useEffect(() => {
    setQueryValue(searchQuery);
  }, [searchQuery]);

  useEffect(() => {
    setCurrentStatusFilter(statusFilter);
  }, [statusFilter]);

  // 🦠 BACTERIAL TOAST HANDLING - Clean feedback
  useEffect(() => {
    if (fetcher.state === 'idle' && fetcher.data) {
      if ('error' in fetcher.data && fetcher.data.error) {
        setToastContent(fetcher.data.error);
        setIsToastError(true);
        setToastActive(true);
      } else if ('message' in fetcher.data && fetcher.data.message) {
        setToastContent(fetcher.data.message);
        setIsToastError(false);
        setToastActive(true);
      }
    }
  }, [fetcher.data, fetcher.state]);

  // 🦠 BACTERIAL DATA TRANSFORMATION - Clean date handling
  const jobs: JobWithDates[] = serializedJobs.map(job => ({
    ...job,
    createdAt: new Date(job.createdAt),
    updatedAt: new Date(job.updatedAt),
    completedAt: job.completedAt ? new Date(job.completedAt) : null,
    startedAt: job.startedAt ? new Date(job.startedAt) : null,
    scheduledAt: job.scheduledAt ? new Date(job.scheduledAt) : null,
    lockedAt: job.lockedAt ? new Date(job.lockedAt) : null,
  }));

  // 🦠 BACTERIAL JOB CARDS DATA - Transform for JobCard components
  const jobCardsData = jobs.map(job => ({
    id: job.id,
    type: job.type,
    status: job.status,
    createdAt: job.createdAt,
    startedAt: job.startedAt,
    completedAt: job.completedAt,
    scheduledAt: job.scheduledAt,
    errorMessage: job.errorMessage,
    data: job.data,
    onRetry: (jobId: string) => {
      fetcher.submit({ _action: "retryJob", id: jobId }, { method: "post" });
    },
    onCancel: (jobId: string) => {
      fetcher.submit({ _action: "cancelJob", id: jobId }, { method: "post" });
    },
    isActionInProgress: fetcher.state !== 'idle' && fetcher.formData?.get('id') === job.id,
    LinkComponent: RemixLink,
    detailsUrl: `/app/jobs/${job.id}`,
    enhanced: true
  }));

  // 🦠 BACTERIAL HANDLERS - Clean, focused handlers
  const handlePageChange = (page: number) => {
    const formData = new FormData();
    formData.set("query", queryValue);
    formData.set("status", currentStatusFilter);
    formData.set("page", page.toString());
    submit(formData, { method: "get", replace: true });
  };

  const handleQueryChange = (value: string) => {
    setQueryValue(value);
    const formData = new FormData();
    formData.set("query", value);
    formData.set("status", currentStatusFilter);
    formData.set("page", "1");
    submit(formData, { method: "get", replace: true });
  };

  const handleStatusChange = (value: string) => {
    setCurrentStatusFilter(value);
    const formData = new FormData();
    formData.set("query", queryValue);
    formData.set("status", value);
    formData.set("page", "1");
    submit(formData, { method: "get", replace: true });
  };

  const handleClearFilters = () => {
    setQueryValue("");
    setCurrentStatusFilter("all");
    submit({ query: "", status: "all", page: "1" }, { method: "get", replace: true });
  };

  // 🦠 BACTERIAL STATUS COUNTS - For filter summary
  const statusCounts = getStatusCounts(jobs);

  return (
    <Page title="Job Status">
      <Layout>
        <Layout.Section>
          {/* 🦠 BACTERIAL JOB FILTERS */}
          <JobFilters
            searchQuery={queryValue}
            statusFilter={currentStatusFilter}
            availableStatuses={availableStatuses}
            onSearchChange={handleQueryChange}
            onStatusFilterChange={handleStatusChange}
            onClearFilters={handleClearFilters}
            lastRefreshTime={formattedTime}
            isRefreshing={isFlashing}
          />
        </Layout.Section>

        <Layout.Section>
          {/* 🦠 BACTERIAL FILTER SUMMARY */}
          <JobFilterSummary
            totalCount={jobs.length}
            filteredCount={jobs.length}
            filters={{ searchQuery: queryValue, statusFilter: currentStatusFilter }}
            statusCounts={statusCounts}
          />
        </Layout.Section>

        <Layout.Section>
          {/* 🦠 BACTERIAL JOB DISPLAY */}
          {jobCardsData.length > 0 ? (
            <BlockStack gap="400">
              <JobGrid jobs={jobCardsData} enhanced={true} />

              {/* 🦠 BACTERIAL PAGINATION */}
              {totalPages > 1 && (
                <NumberedPagination
                  currentPage={currentPage}
                  totalPages={totalPages}
                  onPageChange={handlePageChange}
                />
              )}
            </BlockStack>
          ) : (
            <EmptyJobs
              message="No jobs match your current filters. Try adjusting your search or status filter."
              actionText="Clear Filters"
              onAction={handleClearFilters}
              showClearFilters={true}
            />
          )}
        </Layout.Section>
      </Layout>

      {/* 🦠 BACTERIAL TOAST FEEDBACK */}
      {toastActive && (
        <Toast content={toastContent} error={isToastError} onDismiss={handleToastDismiss} />
      )}
      <div style={{ paddingBottom: 'var(--p-space-800)' }} />
    </Page>
  );
}
