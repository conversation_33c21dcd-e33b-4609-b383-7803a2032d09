import { type LoaderFunctionArgs, type ActionFunctionArgs } from "@remix-run/node";
import { useLoaderData, useFetcher } from "@remix-run/react";
import { useState, useEffect } from "react";
import { Layout, Page, Text, BlockStack, Toast } from "@shopify/polaris";
import { authenticate } from "../shopify.server";
import prisma from "../db.server";

import { usePollingWithFeedback } from "app/hooks/usePollingWithFeedback";
import { addJob } from "../queues/jobQueue.server";
import { JobDetailsCard, JobTimeline, JobDataViewer, formatJobName } from "../components/JobDetailsCard";
import { JobActions } from "../components/JobActions";

export const action = async ({ request }: ActionFunctionArgs) => {
  await authenticate.admin(request);
  const formData = await request.formData();
  const { _action, id } = Object.fromEntries(formData);

  const jobId = id as string;

  switch (_action) {
    case "retryJob":
      const jobToRetry = await prisma.job.findUnique({ where: { id: jobId } });
      if (jobToRetry) {
        await addJob({
          shop: jobToRetry.shop,
          jobType: jobToRetry.type,
          data: JSON.parse(jobToRetry.data),
        });
        return new Response(JSON.stringify({ ok: true, message: "Job has been re-queued successfully." }), {
          headers: { "Content-Type": "application/json" }
        });
      }
      return new Response(JSON.stringify({ error: "Job not found." }), {
        status: 404,
        headers: { "Content-Type": "application/json" }
      });

    case "cancelJob":
      await prisma.job.update({
        where: { id: jobId },
        data: { status: "canceled", errorMessage: "Job canceled by user." },
      });
      return new Response(JSON.stringify({ ok: true, message: "Job has been canceled successfully." }), {
        headers: { "Content-Type": "application/json" }
      });

    default:
      return new Response(JSON.stringify({ error: "Invalid action" }), {
        status: 400,
        headers: { "Content-Type": "application/json" }
      });
  }
};

export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  await authenticate.admin(request);
  const { id } = params;

  if (!id) {
    throw new Response("Job ID not provided", { status: 400 });
  }

  const job = await prisma.job.findUnique({
    where: { id },
  });

  if (!job) {
    throw new Response("Job not found", { status: 404 });
  }

  // Rehydrate dates for client-side use
  const serializedJob = {
    ...job,
    createdAt: job.createdAt.toISOString(),
    updatedAt: job.updatedAt.toISOString(),
    startedAt: job.startedAt?.toISOString() || null,
    completedAt: job.completedAt?.toISOString() || null,
    scheduledAt: job.scheduledAt?.toISOString() || null,
    lockedAt: job.lockedAt?.toISOString() || null,
  };

  return new Response(JSON.stringify({ job: serializedJob }), {
    headers: { "Content-Type": "application/json" }
  });
};

export default function JobDetails() {
  const { job: serializedJob } = useLoaderData<typeof loader>();
  const fetcher = useFetcher<typeof action>();

  // Rehydrate dates from serialized strings
  const job = {
    ...serializedJob,
    createdAt: new Date(serializedJob.createdAt),
    updatedAt: new Date(serializedJob.updatedAt),
    startedAt: serializedJob.startedAt ? new Date(serializedJob.startedAt) : null,
    completedAt: serializedJob.completedAt ? new Date(serializedJob.completedAt) : null,
    scheduledAt: serializedJob.scheduledAt ? new Date(serializedJob.scheduledAt) : null,
    lockedAt: serializedJob.lockedAt ? new Date(serializedJob.lockedAt) : null,
  };

  const [toastContent, setToastContent] = useState("");
  const [isToastError, setIsToastError] = useState(false);
  const [toastActive, setToastActive] = useState(false);

  const handleToastDismiss = () => setToastActive(false);

  const { formattedTime, isFlashing } = usePollingWithFeedback(30000);

  useEffect(() => {
    if (fetcher.state === 'idle' && fetcher.data) {
      if ('error' in fetcher.data && fetcher.data.error) {
        setToastContent(fetcher.data.error);
        setIsToastError(true);
        setToastActive(true);
      } else if ('message' in fetcher.data && fetcher.data.message) {
        setToastContent(fetcher.data.message);
        setIsToastError(false);
        setToastActive(true);
      }
    }
  }, [fetcher.data, fetcher.state]);

  // 🦠 BACTERIAL COPY HANDLER - Clean clipboard functionality
  const handleCopy = async (text: string, message: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setToastContent(message);
      setIsToastError(false);
      setToastActive(true);
    } catch (err) {
      setToastContent("Failed to copy.");
      setIsToastError(true);
      setToastActive(true);
    }
  };

  // 🦠 BACTERIAL ACTION HANDLERS - Clean, focused handlers
  const isSubmitting = fetcher.state !== 'idle';

  const handleRetry = (jobId: string) => {
    fetcher.submit({ _action: "retryJob", id: jobId }, { method: "post" });
  };

  const handleCancel = (jobId: string) => {
    fetcher.submit({ _action: "cancelJob", id: jobId }, { method: "post" });
  };

  const handleRefresh = () => {
    window.location.reload();
  };

  return (
    <Page
      title={`Job: ${formatJobName(job.type)}`}
      backAction={{ content: "Jobs", url: "/app/jobs" }}
    >
      <Layout>
        <Layout.Section>
          <BlockStack gap="400">
            {/* 🦠 BACTERIAL JOB DETAILS */}
            <JobDetailsCard
              id={job.id}
              shop={job.shop}
              type={job.type}
              status={job.status}
              createdAt={job.createdAt}
              startedAt={job.startedAt}
              completedAt={job.completedAt}
              scheduledAt={job.scheduledAt}
              updatedAt={job.updatedAt}
              retryCount={job.retryCount}
              maxRetries={job.maxRetries}
              errorMessage={job.errorMessage}
              data={job.data}
              onCopy={handleCopy}
              enhanced={true}
            />

            {/* 🦠 BACTERIAL JOB TIMELINE */}
            <JobTimeline
              createdAt={job.createdAt}
              startedAt={job.startedAt}
              completedAt={job.completedAt}
              scheduledAt={job.scheduledAt}
              status={job.status}
              enhanced={true}
            />

            {/* 🦠 BACTERIAL JOB ACTIONS */}
            <JobActions
              jobId={job.id}
              status={job.status}
              onRetry={handleRetry}
              onCancel={handleCancel}
              onRefresh={handleRefresh}
              isActionInProgress={isSubmitting}
              enhanced={true}
            />

            {/* 🦠 BACTERIAL JOB DATA VIEWER */}
            <JobDataViewer
              data={job.data}
              onCopy={handleCopy}
              enhanced={true}
            />
          </BlockStack>
        </Layout.Section>

        <Layout.Section>
          <Text as="p" variant="bodySm" tone="subdued" alignment="end">
            Last updated: <Text as="span" tone={isFlashing ? "success" : "subdued"}>{formattedTime}</Text>
          </Text>
        </Layout.Section>
      </Layout>

      {/* 🦠 BACTERIAL TOAST FEEDBACK */}
      {toastActive && (
        <Toast content={toastContent} error={isToastError} onDismiss={handleToastDismiss} />
      )}
    </Page>
  );
}
