// 🦠 BACTERIAL DASHBOARD METRICS COMPONENTS
// Self-contained, reusable dashboard widgets and metrics

import { Card, BlockStack, InlineStack, Text, Badge, Icon, ProgressBar } from "@shopify/polaris";
import { ArrowUpIcon, ArrowDownIcon, ClockIcon, PlayIcon, CheckIcon, AlertTriangleIcon } from "@shopify/polaris-icons";

// 🦠 BACTERIAL INTERFACES - Pure, self-contained
export interface MetricCardProps {
  /** Metric title */
  title: string;
  /** Metric value */
  value: number | string;
  /** Metric description */
  description?: string;
  /** Previous value for trend calculation */
  previousValue?: number;
  /** Metric icon */
  icon?: any;
  /** Custom formatting for value */
  formatter?: (value: number) => string;
  /** Show trend indicator */
  showTrend?: boolean;
  /** Enhanced features */
  enhanced?: boolean;
}

export interface MetricGridProps {
  /** Array of metrics */
  metrics: MetricCardProps[];
  /** Grid columns */
  columns?: 2 | 3 | 4;
  /** Enhanced features */
  enhanced?: boolean;
}

export interface StatusMetricsProps {
  /** Job status counts */
  statusCounts: Record<string, number>;
  /** Total jobs */
  totalJobs: number;
  /** Show percentages */
  showPercentages?: boolean;
  /** Enhanced features */
  enhanced?: boolean;
}

export interface TrendIndicatorProps {
  /** Current value */
  current: number;
  /** Previous value */
  previous: number;
  /** Show percentage change */
  showPercentage?: boolean;
}

// 🦠 BACTERIAL UTILITY - Calculate trend
export function calculateTrend(current: number, previous: number): {
  direction: 'up' | 'down' | 'neutral';
  percentage: number;
  isPositive: boolean;
} {
  if (previous === 0) {
    return { direction: 'neutral', percentage: 0, isPositive: true };
  }
  
  const change = ((current - previous) / previous) * 100;
  const direction = change > 0 ? 'up' : change < 0 ? 'down' : 'neutral';
  const isPositive = change >= 0;
  
  return {
    direction,
    percentage: Math.abs(change),
    isPositive
  };
}

// 🦠 BACTERIAL UTILITY - Format large numbers
export function formatLargeNumber(value: number): string {
  if (value >= 1000000) {
    return `${(value / 1000000).toFixed(1)}M`;
  }
  if (value >= 1000) {
    return `${(value / 1000).toFixed(1)}K`;
  }
  return value.toString();
}

// 🦠 BACTERIAL UTILITY - Get status color
export function getStatusColor(status: string): "success" | "critical" | "warning" | "info" {
  switch (status.toLowerCase()) {
    case 'completed':
    case 'success':
      return 'success';
    case 'failed':
    case 'error':
      return 'critical';
    case 'processing':
    case 'retrying':
      return 'warning';
    default:
      return 'info';
  }
}

// 🦠 BACTERIAL TREND INDICATOR - Visual trend display
export function TrendIndicator({ current, previous, showPercentage = true }: TrendIndicatorProps) {
  const trend = calculateTrend(current, previous);
  
  if (trend.direction === 'neutral') {
    return null;
  }
  
  const icon = trend.direction === 'up' ? ArrowUpIcon : ArrowDownIcon;
  const tone = trend.isPositive ? 'success' : 'critical';
  
  return (
    <InlineStack gap="050" blockAlign="center">
      <Icon source={icon} tone={tone} />
      {showPercentage && (
        <Text variant="bodySm" tone={tone} as="span">
          {trend.percentage.toFixed(1)}%
        </Text>
      )}
    </InlineStack>
  );
}

// 🦠 BACTERIAL METRIC CARD - Enhanced metric display
export function MetricCard({
  title,
  value,
  description,
  previousValue,
  icon,
  formatter,
  showTrend = true,
  enhanced = true
}: MetricCardProps) {
  const numericValue = typeof value === 'number' ? value : parseInt(value.toString(), 10) || 0;
  const formattedValue = formatter ? formatter(numericValue) : value.toString();
  const numericPrevious = previousValue || 0;
  
  return (
    <Card>
      <BlockStack gap="300">
        {/* Header with icon and title */}
        <InlineStack align="space-between" blockAlign="start">
          <InlineStack gap="100" blockAlign="center">
            {icon && (
              <div style={{ minWidth: '16px', display: 'flex', alignItems: 'center' }}>
                <Icon source={icon} tone="subdued" />
              </div>
            )}
            <Text variant="headingMd" as="h3">
              {title}
            </Text>
          </InlineStack>
          
          {showTrend && previousValue && (
            <TrendIndicator current={numericValue} previous={numericPrevious} />
          )}
        </InlineStack>
        
        {/* Main value */}
        <Text variant="heading2xl" as="p">
          {formattedValue}
        </Text>
        
        {/* Description */}
        {description && (
          <Text variant="bodyMd" tone="subdued" as="p">
            {description}
          </Text>
        )}
        
        {/* Enhanced trend details */}
        {enhanced && showTrend && previousValue && (
          <Card background="bg-surface-secondary">
            <Text variant="bodySm" tone="subdued" as="p">
              {(() => {
                const trend = calculateTrend(numericValue, numericPrevious);
                const changeText = trend.isPositive ? 'increase' : 'decrease';
                return `${trend.percentage.toFixed(1)}% ${changeText} from previous period`;
              })()}
            </Text>
          </Card>
        )}
      </BlockStack>
    </Card>
  );
}

// 🦠 BACTERIAL METRIC GRID - Responsive metric layout
export function MetricGrid({ metrics, columns = 2, enhanced = true }: MetricGridProps) {
  const gridStyle = {
    display: 'grid',
    gridTemplateColumns: `repeat(${columns}, 1fr)`,
    gap: 'var(--p-space-400)',
    '@media (max-width: 768px)': {
      gridTemplateColumns: '1fr'
    }
  };
  
  return (
    <div style={gridStyle}>
      {metrics.map((metric, index) => (
        <MetricCard key={index} {...metric} enhanced={enhanced} />
      ))}
    </div>
  );
}

// 🦠 BACTERIAL STATUS METRICS - Job status breakdown
export function StatusMetrics({ 
  statusCounts, 
  totalJobs, 
  showPercentages = true, 
  enhanced = true 
}: StatusMetricsProps) {
  const statusConfig = {
    completed: { icon: CheckIcon, tone: 'success' as const, label: 'Completed' },
    failed: { icon: AlertTriangleIcon, tone: 'critical' as const, label: 'Failed' },
    processing: { icon: PlayIcon, tone: 'warning' as const, label: 'Processing' },
    pending: { icon: ClockIcon, tone: 'info' as const, label: 'Pending' }
  };
  
  return (
    <Card>
      <BlockStack gap="400">
        <Text variant="headingMd" as="h3">
          Job Status Breakdown
        </Text>
        
        <BlockStack gap="200">
          {Object.entries(statusCounts)
            .filter(([status]) => status !== 'all')
            .map(([status, count]) => {
              const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
              const percentage = totalJobs > 0 ? (count / totalJobs) * 100 : 0;
              
              return (
                <InlineStack key={status} align="space-between" blockAlign="center">
                  <InlineStack gap="100" blockAlign="center">
                    <div style={{ minWidth: '16px', display: 'flex', alignItems: 'center' }}>
                      <Icon source={config.icon} tone={config.tone} />
                    </div>
                    <Text variant="bodyMd" as="span">
                      {config.label}
                    </Text>
                  </InlineStack>
                  
                  <InlineStack gap="200" blockAlign="center">
                    {showPercentages && (
                      <Text variant="bodySm" tone="subdued" as="span">
                        {percentage.toFixed(1)}%
                      </Text>
                    )}
                    <Badge tone={config.tone}>
                      {count.toString()}
                    </Badge>
                  </InlineStack>
                </InlineStack>
              );
            })}
        </BlockStack>
        
        {enhanced && totalJobs > 0 && (
          <BlockStack gap="200">
            <Text variant="bodySm" fontWeight="medium" as="h4">
              Success Rate
            </Text>
            <ProgressBar 
              progress={(statusCounts.completed || 0) / totalJobs * 100} 
              tone="success"
            />
            <Text variant="bodySm" tone="subdued" as="p">
              {((statusCounts.completed || 0) / totalJobs * 100).toFixed(1)}% of jobs completed successfully
            </Text>
          </BlockStack>
        )}
      </BlockStack>
    </Card>
  );
}

// 🦠 BACTERIAL QUICK STATS - Compact metrics display
export interface QuickStatsProps {
  /** Stats data */
  stats: Array<{
    label: string;
    value: number | string;
    icon?: any;
    tone?: "success" | "critical" | "warning" | "info";
  }>;
  /** Horizontal layout */
  horizontal?: boolean;
}

export function QuickStats({ stats, horizontal = true }: QuickStatsProps) {
  const Container = horizontal ? InlineStack : BlockStack;
  
  return (
    <Card>
      <Container gap="400" wrap={horizontal}>
        {stats.map((stat, index) => (
          <BlockStack key={index} gap="100" align="center">
            {stat.icon && (
              <Icon source={stat.icon} tone={stat.tone || 'subdued'} />
            )}
            <Text variant="headingLg" as="p" alignment="center">
              {stat.value}
            </Text>
            <Text variant="bodySm" tone="subdued" as="p" alignment="center">
              {stat.label}
            </Text>
          </BlockStack>
        ))}
      </Container>
    </Card>
  );
}

// 🦠 BACTERIAL HOOK - Dashboard metrics state
export function useDashboardMetrics(rawStats: any) {
  const metrics: MetricCardProps[] = [
    {
      title: "Active Automations",
      value: rawStats.activeAutomations || 0,
      description: "Currently enabled tasks",
      icon: PlayIcon,
      showTrend: false
    },
    {
      title: "Total Runs",
      value: rawStats.totalRuns || 0,
      description: "Executions in the past 30 days",
      icon: ClockIcon,
      formatter: formatLargeNumber,
      showTrend: false
    }
  ];
  
  return { metrics };
}
