// 🦠 BACTERIAL FORM ERROR HANDLER COMPONENTS
// Self-contained, reusable form error handling and validation

import { Banner, BlockStack, Text, InlineStack, Icon } from "@shopify/polaris";
import { AlertTriangleIcon, CheckCircleIcon, InfoIcon } from "@shopify/polaris-icons";

// 🦠 BACTERIAL INTERFACES - Pure, self-contained
export interface FormError {
  field?: string;
  message: string;
  type?: 'error' | 'warning' | 'info';
}

export interface FormErrorsProps {
  /** Array of form errors */
  errors: FormError[];
  /** Error display style */
  style?: 'banner' | 'inline' | 'list';
  /** Show error icons */
  showIcons?: boolean;
  /** Custom title for error section */
  title?: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: FormError[];
  warnings: FormError[];
}

export interface FormValidationProps {
  /** Validation result */
  validation: ValidationResult;
  /** Show warnings */
  showWarnings?: boolean;
  /** Show success state when valid */
  showSuccess?: boolean;
  /** Custom success message */
  successMessage?: string;
}

// 🦠 BACTERIAL UTILITY - Get error icon
export function getErrorIcon(type: FormError['type'] = 'error') {
  switch (type) {
    case 'warning':
      return AlertTriangleIcon;
    case 'info':
      return InfoIcon;
    case 'error':
    default:
      return AlertTriangleIcon;
  }
}

// 🦠 BACTERIAL UTILITY - Get error tone
export function getErrorTone(type: FormError['type'] = 'error'): "critical" | "warning" | "info" {
  switch (type) {
    case 'warning':
      return 'warning';
    case 'info':
      return 'info';
    case 'error':
    default:
      return 'critical';
  }
}

// 🦠 BACTERIAL UTILITY - Format field name
export function formatFieldName(field: string): string {
  return field
    .split(/[_-]/)
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

// 🦠 BACTERIAL UTILITY - Group errors by type
export function groupErrorsByType(errors: FormError[]): {
  errors: FormError[];
  warnings: FormError[];
  info: FormError[];
} {
  return {
    errors: errors.filter(e => e.type === 'error' || !e.type),
    warnings: errors.filter(e => e.type === 'warning'),
    info: errors.filter(e => e.type === 'info')
  };
}

// 🦠 BACTERIAL ERROR DISPLAY - Banner style
export function FormErrorBanner({ 
  errors, 
  title = "Please fix the following issues:",
  showIcons = true 
}: Omit<FormErrorsProps, 'style'>) {
  if (errors.length === 0) return null;

  const grouped = groupErrorsByType(errors);
  
  return (
    <BlockStack gap="200">
      {grouped.errors.length > 0 && (
        <Banner tone="critical" title={title}>
          <BlockStack gap="100">
            {grouped.errors.map((error, index) => (
              <InlineStack key={index} gap="100" blockAlign="center">
                {showIcons && <Icon source={getErrorIcon(error.type)} tone="critical" />}
                <Text as="span">
                  {error.field && <strong>{formatFieldName(error.field)}:</strong>} {error.message}
                </Text>
              </InlineStack>
            ))}
          </BlockStack>
        </Banner>
      )}
      
      {grouped.warnings.length > 0 && (
        <Banner tone="warning" title="Warnings:">
          <BlockStack gap="100">
            {grouped.warnings.map((warning, index) => (
              <InlineStack key={index} gap="100" blockAlign="center">
                {showIcons && <Icon source={getErrorIcon(warning.type)} tone="warning" />}
                <Text as="span">
                  {warning.field && <strong>{formatFieldName(warning.field)}:</strong>} {warning.message}
                </Text>
              </InlineStack>
            ))}
          </BlockStack>
        </Banner>
      )}
      
      {grouped.info.length > 0 && (
        <Banner tone="info" title="Information:">
          <BlockStack gap="100">
            {grouped.info.map((info, index) => (
              <InlineStack key={index} gap="100" blockAlign="center">
                {showIcons && <Icon source={getErrorIcon(info.type)} tone="info" />}
                <Text as="span">
                  {info.field && <strong>{formatFieldName(info.field)}:</strong>} {info.message}
                </Text>
              </InlineStack>
            ))}
          </BlockStack>
        </Banner>
      )}
    </BlockStack>
  );
}

// 🦠 BACTERIAL ERROR DISPLAY - Inline style
export function FormErrorInline({ 
  errors, 
  showIcons = true 
}: Omit<FormErrorsProps, 'style' | 'title'>) {
  if (errors.length === 0) return null;

  return (
    <BlockStack gap="100">
      {errors.map((error, index) => (
        <InlineStack key={index} gap="100" blockAlign="center">
          {showIcons && <Icon source={getErrorIcon(error.type)} tone={getErrorTone(error.type)} />}
          <Text variant="bodySm" tone={getErrorTone(error.type)} as="span">
            {error.field && <strong>{formatFieldName(error.field)}:</strong>} {error.message}
          </Text>
        </InlineStack>
      ))}
    </BlockStack>
  );
}

// 🦠 BACTERIAL ERROR DISPLAY - List style
export function FormErrorList({ 
  errors, 
  title = "Issues found:",
  showIcons = true 
}: Omit<FormErrorsProps, 'style'>) {
  if (errors.length === 0) return null;

  const grouped = groupErrorsByType(errors);

  return (
    <BlockStack gap="300">
      {title && <Text variant="headingSm" as="h3">{title}</Text>}
      
      {grouped.errors.length > 0 && (
        <BlockStack gap="100">
          {grouped.errors.map((error, index) => (
            <InlineStack key={index} gap="100" blockAlign="start">
              {showIcons && <Icon source={getErrorIcon(error.type)} tone="critical" />}
              <Text variant="bodySm" as="span">
                {error.field && <strong>{formatFieldName(error.field)}:</strong>} {error.message}
              </Text>
            </InlineStack>
          ))}
        </BlockStack>
      )}
      
      {grouped.warnings.length > 0 && (
        <BlockStack gap="100">
          <Text variant="bodyMd" tone="warning" as="h4">Warnings:</Text>
          {grouped.warnings.map((warning, index) => (
            <InlineStack key={index} gap="100" blockAlign="start">
              {showIcons && <Icon source={getErrorIcon(warning.type)} tone="warning" />}
              <Text variant="bodySm" as="span">
                {warning.field && <strong>{formatFieldName(warning.field)}:</strong>} {warning.message}
              </Text>
            </InlineStack>
          ))}
        </BlockStack>
      )}
    </BlockStack>
  );
}

// 🦠 BACTERIAL ERROR HANDLER - Main component
export function FormErrors({ 
  errors, 
  style = 'banner', 
  showIcons = true, 
  title 
}: FormErrorsProps) {
  switch (style) {
    case 'inline':
      return <FormErrorInline errors={errors} showIcons={showIcons} />;
    case 'list':
      return <FormErrorList errors={errors} title={title} showIcons={showIcons} />;
    case 'banner':
    default:
      return <FormErrorBanner errors={errors} title={title} showIcons={showIcons} />;
  }
}

// 🦠 BACTERIAL VALIDATION DISPLAY - Shows validation state
export function FormValidation({
  validation,
  showWarnings = true,
  showSuccess = true,
  successMessage = "All fields are valid"
}: FormValidationProps) {
  const hasErrors = validation.errors.length > 0;
  const hasWarnings = validation.warnings.length > 0;
  const isValid = validation.isValid && !hasErrors;

  return (
    <BlockStack gap="200">
      {hasErrors && (
        <FormErrors 
          errors={validation.errors} 
          style="banner" 
          title="Please fix these errors:" 
        />
      )}
      
      {showWarnings && hasWarnings && (
        <FormErrors 
          errors={validation.warnings} 
          style="banner" 
          title="Please review these warnings:" 
        />
      )}
      
      {showSuccess && isValid && !hasWarnings && (
        <Banner tone="success">
          <InlineStack gap="100" blockAlign="center">
            <Icon source={CheckCircleIcon} tone="success" />
            <Text as="span">{successMessage}</Text>
          </InlineStack>
        </Banner>
      )}
    </BlockStack>
  );
}

// 🦠 BACTERIAL UTILITY - Create form error
export function createFormError(
  message: string, 
  field?: string, 
  type: FormError['type'] = 'error'
): FormError {
  return { message, field, type };
}

// 🦠 BACTERIAL UTILITY - Validate required fields
export function validateRequiredFields(
  data: Record<string, any>, 
  requiredFields: string[]
): FormError[] {
  const errors: FormError[] = [];
  
  requiredFields.forEach(field => {
    const value = data[field];
    if (!value || (typeof value === 'string' && value.trim() === '')) {
      errors.push(createFormError(
        'This field is required',
        field,
        'error'
      ));
    }
  });
  
  return errors;
}

// 🦠 BACTERIAL HOOK - Form validation state
export function useFormValidation(initialErrors: FormError[] = []) {
  const [errors, setErrors] = useState<FormError[]>(initialErrors);
  const [warnings, setWarnings] = useState<FormError[]>([]);

  const addError = (error: FormError) => {
    setErrors(prev => [...prev, error]);
  };

  const addWarning = (warning: FormError) => {
    setWarnings(prev => [...prev, warning]);
  };

  const clearErrors = (field?: string) => {
    if (field) {
      setErrors(prev => prev.filter(e => e.field !== field));
    } else {
      setErrors([]);
    }
  };

  const clearWarnings = (field?: string) => {
    if (field) {
      setWarnings(prev => prev.filter(w => w.field !== field));
    } else {
      setWarnings([]);
    }
  };

  const clearAll = () => {
    setErrors([]);
    setWarnings([]);
  };

  const validation: ValidationResult = {
    isValid: errors.length === 0,
    errors,
    warnings
  };

  return {
    validation,
    addError,
    addWarning,
    clearErrors,
    clearWarnings,
    clearAll,
    setErrors,
    setWarnings
  };
}
