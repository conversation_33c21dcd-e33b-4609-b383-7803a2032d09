// 🦠 BACTERIAL JOB FILTERS COMPONENT
// Self-contained, reusable job filtering and search

import { useState } from "react";
import { Card, BlockStack, InlineStack, Text, Button, Badge, ChoiceList, Collapsible, Select, TextField } from "@shopify/polaris";
import { FilterIcon, RefreshIcon } from "@shopify/polaris-icons";
import type { JobStatus, JobType } from "@prisma/client";

// 🦠 BACTERIAL INTERFACES - Pure, self-contained
export interface JobFilterProps {
  /** Search query */
  searchQuery: string;
  /** Selected status filter */
  statusFilter: string;
  /** Available job statuses */
  availableStatuses: JobStatus[];
  /** Search change handler */
  onSearchChange: (query: string) => void;
  /** Status filter change handler */
  onStatusFilterChange: (status: string) => void;
  /** Clear all filters handler */
  onClearFilters: () => void;
  /** Auto-refresh enabled */
  autoRefresh?: boolean;
  /** Auto-refresh toggle handler */
  onAutoRefreshToggle?: (enabled: boolean) => void;
  /** Last refresh time */
  lastRefreshTime?: string;
  /** Is refreshing */
  isRefreshing?: boolean;
}

export interface JobWithFilters {
  id: string;
  type: JobType;
  status: JobStatus;
  createdAt: Date;
  startedAt: Date | null;
  completedAt: Date | null;
  scheduledAt: Date | null;
  errorMessage: string | null;
  data: string;
}

// 🦠 BACTERIAL STATUS OPTIONS - Pure configuration
const STATUS_OPTIONS = [
  { label: "Pending", value: "pending" },
  { label: "Processing", value: "processing" },
  { label: "Completed", value: "completed" },
  { label: "Failed", value: "failed" },
  { label: "Canceled", value: "canceled" },
  { label: "Scheduled", value: "scheduled" }
];

// 🦠 BACTERIAL JOB TYPE NAMES - Pure mapping
const JOB_TYPE_NAMES: Record<JobType, string> = {
  AUTO_TAG_ORDERS_UTM: "UTM Order Tagging",
  ORDER_COLLECTION_TAG: "Collection Order Tagging",
  COLLECTION_VISIBILITY_UPDATE: "Collection Visibility Update",
  ORDER_CART_ATTRIBUTE_TAG: "Cart Attribute Tagging",
  AUTO_TAG_CUSTOMER_BY_ORDER_TAG: "Customer Order Tag Tagging",
  AUTO_TAG_CUSTOMER_BY_VENDOR: "Customer Vendor Tagging",
  COLLECTION_VISIBILITY_BULK_UPDATE: "Bulk Collection Visibility Update",
  STUCK_JOB_CLEANUP: "Stuck Job Cleanup",
  ORDER_DISCOUNT_TAG: "Order Discount Tagging",
  CANCEL_HIGH_RISK_ORDER: "Cancel High Risk Orders"
};

// 🦠 BACTERIAL UTILITY - Check if filters are active
export function hasActiveJobFilters(filters: Pick<JobFilterProps, 'searchQuery' | 'statusFilter'>): boolean {
  return !!(
    filters.searchQuery.trim() ||
    (filters.statusFilter && filters.statusFilter !== "all")
  );
}

// 🦠 BACTERIAL UTILITY - Filter jobs
export function filterJobs(
  jobs: JobWithFilters[],
  filters: Pick<JobFilterProps, 'searchQuery' | 'statusFilter'>
): JobWithFilters[] {
  return jobs.filter(job => {
    // Search filter
    if (filters.searchQuery.trim()) {
      const query = filters.searchQuery.toLowerCase();
      const jobTypeName = JOB_TYPE_NAMES[job.type]?.toLowerCase() || job.type.toLowerCase();
      const matchesSearch = 
        jobTypeName.includes(query) ||
        job.id.toLowerCase().includes(query) ||
        job.status.toLowerCase().includes(query);
      
      if (!matchesSearch) return false;
    }
    
    // Status filter
    if (filters.statusFilter && filters.statusFilter !== "all") {
      if (job.status !== filters.statusFilter) return false;
    }
    
    return true;
  });
}

// 🦠 BACTERIAL UTILITY - Get filter summary
export function getJobFilterSummary(
  totalCount: number,
  filteredCount: number,
  filters: Pick<JobFilterProps, 'searchQuery' | 'statusFilter'>
): string {
  if (!hasActiveJobFilters(filters)) {
    return `${totalCount} job${totalCount === 1 ? '' : 's'}`;
  }
  
  return `${filteredCount} of ${totalCount} job${totalCount === 1 ? '' : 's'}`;
}

// 🦠 BACTERIAL UTILITY - Get status counts
export function getStatusCounts(jobs: JobWithFilters[]): Record<string, number> {
  const counts: Record<string, number> = { all: jobs.length };
  
  jobs.forEach(job => {
    counts[job.status] = (counts[job.status] || 0) + 1;
  });
  
  return counts;
}

// 🦠 BACTERIAL JOB FILTERS - Main component
export function JobFilters({
  searchQuery,
  statusFilter,
  availableStatuses,
  onSearchChange,
  onStatusFilterChange,
  onClearFilters,
  autoRefresh = false,
  onAutoRefreshToggle,
  lastRefreshTime,
  isRefreshing = false
}: JobFilterProps) {
  const [filtersOpen, setFiltersOpen] = useState(false);
  
  const hasFilters = hasActiveJobFilters({ searchQuery, statusFilter });
  
  const statusOptions = [
    { label: "All Statuses", value: "all" },
    ...availableStatuses.map(status => ({
      label: status.charAt(0).toUpperCase() + status.slice(1),
      value: status
    }))
  ];

  return (
    <Card>
      <BlockStack gap="300">
        {/* Search and Quick Filters */}
        <InlineStack gap="400" wrap={true} blockAlign="end">
          <div style={{ flex: "1 1 300px" }}>
            <TextField
              label="Search Jobs"
              labelHidden
              value={searchQuery}
              onChange={onSearchChange}
              placeholder="Search jobs by type, ID, or status..."
              autoComplete="off"
            />
          </div>
          
          <div style={{ flex: "0 1 200px" }}>
            <Select
              label="Filter by Status"
              labelHidden
              options={statusOptions}
              value={statusFilter}
              onChange={onStatusFilterChange}
            />
          </div>
          
          {/* Auto-refresh and last update */}
          <InlineStack gap="200" blockAlign="center">
            {onAutoRefreshToggle && (
              <Button
                variant={autoRefresh ? "primary" : "secondary"}
                size="slim"
                onClick={() => onAutoRefreshToggle(!autoRefresh)}
                icon={RefreshIcon}
              >
                {autoRefresh ? "Auto" : "Manual"}
              </Button>
            )}
            
            {lastRefreshTime && (
              <Text variant="bodySm" tone="subdued" as="span">
                <Text as="span" tone={isRefreshing ? "success" : "subdued"}>
                  {lastRefreshTime}
                </Text>
              </Text>
            )}
          </InlineStack>
        </InlineStack>
        
        {/* Advanced Filters Toggle */}
        <InlineStack align="space-between" blockAlign="center">
          <Button
            variant="tertiary"
            onClick={() => setFiltersOpen(!filtersOpen)}
            icon={FilterIcon}
          >
            Advanced Filters
          </Button>
          
          {hasFilters && (
            <Button
              variant="plain"
              onClick={onClearFilters}
              size="micro"
            >
              Clear all filters
            </Button>
          )}
        </InlineStack>
        
        {/* Collapsible Advanced Filters */}
        <Collapsible open={filtersOpen} id="job-filters">
          <BlockStack gap="400">
            {/* Status Filter with Counts */}
            <ChoiceList
              title="Job Status"
              choices={STATUS_OPTIONS}
              selected={statusFilter !== "all" ? [statusFilter] : []}
              onChange={(selected) => onStatusFilterChange(selected[0] || "all")}
            />
            
            {/* Quick Status Badges */}
            <BlockStack gap="200">
              <Text variant="bodySm" fontWeight="medium" as="h4">
                Quick Status Filters
              </Text>
              <InlineStack gap="100" wrap>
                <Button
                  variant={statusFilter === "all" ? "primary" : "tertiary"}
                  size="micro"
                  onClick={() => onStatusFilterChange("all")}
                >
                  All
                </Button>
                {availableStatuses.map(status => (
                  <Button
                    key={status}
                    variant={statusFilter === status ? "primary" : "tertiary"}
                    size="micro"
                    onClick={() => onStatusFilterChange(status)}
                  >
                    {status.charAt(0).toUpperCase() + status.slice(1)}
                  </Button>
                ))}
              </InlineStack>
            </BlockStack>
          </BlockStack>
        </Collapsible>
      </BlockStack>
    </Card>
  );
}

// 🦠 BACTERIAL FILTER SUMMARY - Display filter results
export interface JobFilterSummaryProps {
  /** Total job count */
  totalCount: number;
  /** Filtered job count */
  filteredCount: number;
  /** Current filters */
  filters: Pick<JobFilterProps, 'searchQuery' | 'statusFilter'>;
  /** Status counts */
  statusCounts?: Record<string, number>;
}

export function JobFilterSummary({ 
  totalCount, 
  filteredCount, 
  filters,
  statusCounts 
}: JobFilterSummaryProps) {
  const summary = getJobFilterSummary(totalCount, filteredCount, filters);
  
  return (
    <InlineStack align="space-between" blockAlign="center">
      <Text variant="bodySm" tone="subdued" as="span">
        {summary}
      </Text>
      
      {hasActiveJobFilters(filters) && (
        <InlineStack gap="100">
          {filters.searchQuery.trim() && (
            <Badge>{`Search: "${filters.searchQuery}"`}</Badge>
          )}
          {filters.statusFilter && filters.statusFilter !== "all" && (
            <Badge>{`Status: ${filters.statusFilter}`}</Badge>
          )}
        </InlineStack>
      )}
      
      {statusCounts && !hasActiveJobFilters(filters) && (
        <InlineStack gap="100">
          {Object.entries(statusCounts)
            .filter(([status]) => status !== "all")
            .map(([status, count]) => (
              <Badge key={status}>
                {`${status}: ${count}`}
              </Badge>
            ))}
        </InlineStack>
      )}
    </InlineStack>
  );
}

// 🦠 BACTERIAL HOOK - Job filter state management
export function useJobFilters() {
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [autoRefresh, setAutoRefresh] = useState(false);

  const clearFilters = () => {
    setSearchQuery("");
    setStatusFilter("all");
  };

  const filters = {
    searchQuery,
    statusFilter
  };

  return {
    filters,
    autoRefresh,
    setSearchQuery,
    setStatusFilter,
    setAutoRefresh,
    clearFilters,
    hasActiveFilters: hasActiveJobFilters(filters)
  };
}
