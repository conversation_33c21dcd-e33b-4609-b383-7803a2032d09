// 🦠 BACTERIAL JOB DETAILS COMPONENTS
// Self-contained, reusable job details display

import { Card, BlockStack, InlineStack, Text, Button, Badge, Icon, Tooltip } from "@shopify/polaris";
import { ClipboardIcon, ClockIcon, PlayIcon, CalendarIcon, AlertTriangleIcon } from "@shopify/polaris-icons";
import type { JobStatus, JobType } from "@prisma/client";
import { formatJobName, getMessageDisplayInfo } from "./JobCard";

// 🦠 BACTERIAL RE-EXPORT - Make formatJobName available
export { formatJobName } from "./JobCard";

// 🦠 BACTERIAL INTERFACES - Pure, self-contained
export interface JobDetailsProps {
  /** Job ID */
  id: string;
  /** Shop domain */
  shop: string;
  /** Job type */
  type: JobType;
  /** Job status */
  status: JobStatus;
  /** Creation date */
  createdAt: Date;
  /** Start date */
  startedAt: Date | null;
  /** Completion date */
  completedAt: Date | null;
  /** Scheduled date */
  scheduledAt: Date | null;
  /** Last update date */
  updatedAt: Date | null;
  /** Retry count */
  retryCount: number;
  /** Max retries */
  maxRetries: number;
  /** Error message */
  errorMessage: string | null;
  /** Job data */
  data: string;
  /** Copy handler */
  onCopy: (text: string, message: string) => void;
  /** Enhanced features */
  enhanced?: boolean;
}

export interface JobTimelineProps {
  /** Job creation date */
  createdAt: Date;
  /** Job start date */
  startedAt: Date | null;
  /** Job completion date */
  completedAt: Date | null;
  /** Job scheduled date */
  scheduledAt: Date | null;
  /** Job status */
  status: JobStatus;
  /** Show enhanced timeline */
  enhanced?: boolean;
}

export interface JobDataViewerProps {
  /** Job data as JSON string */
  data: string;
  /** Copy handler */
  onCopy: (text: string, message: string) => void;
  /** Show enhanced features */
  enhanced?: boolean;
}

// 🦠 BACTERIAL UTILITY - Format duration
export function formatDuration(startDate: Date, endDate: Date | null = null): string {
  const end = endDate || new Date();
  const durationMs = end.getTime() - startDate.getTime();
  const durationSecs = Math.floor(durationMs / 1000);
  
  if (durationSecs < 60) return `${durationSecs}s`;
  
  const durationMins = Math.floor(durationSecs / 60);
  if (durationMins < 60) return `${durationMins}m ${durationSecs % 60}s`;
  
  const durationHours = Math.floor(durationMins / 60);
  if (durationHours < 24) return `${durationHours}h ${durationMins % 60}m`;
  
  const durationDays = Math.floor(durationHours / 24);
  return `${durationDays}d ${durationHours % 24}h`;
}

// 🦠 BACTERIAL UTILITY - Format relative time
export function formatRelativeTime(date: Date): string {
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();
  const diffMins = Math.floor(diffMs / (1000 * 60));
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
  
  if (diffMins < 1) return "Just now";
  if (diffMins < 60) return `${diffMins} minute${diffMins === 1 ? '' : 's'} ago`;
  if (diffHours < 24) return `${diffHours} hour${diffHours === 1 ? '' : 's'} ago`;
  if (diffDays < 7) return `${diffDays} day${diffDays === 1 ? '' : 's'} ago`;
  
  return date.toLocaleDateString();
}

// 🦠 BACTERIAL UTILITY - Parse and format job data
export function parseJobData(data: string): { parsed: any; formatted: string; isValid: boolean } {
  try {
    const parsed = JSON.parse(data);
    const formatted = JSON.stringify(parsed, null, 2);
    return { parsed, formatted, isValid: true };
  } catch {
    return { parsed: null, formatted: data, isValid: false };
  }
}

// 🦠 BACTERIAL UTILITY - Get retry progress
export function getRetryProgress(retryCount: number, maxRetries: number): {
  percentage: number;
  color: "success" | "warning" | "critical";
  label: string;
} {
  const percentage = maxRetries > 0 ? (retryCount / maxRetries) * 100 : 0;
  
  let color: "success" | "warning" | "critical" = "success";
  if (percentage >= 80) color = "critical";
  else if (percentage >= 50) color = "warning";
  
  return {
    percentage,
    color,
    label: `${retryCount} of ${maxRetries} retries used`
  };
}

// 🦠 BACTERIAL JOB TIMELINE - Visual timeline component
export function JobTimeline({ 
  createdAt, 
  startedAt, 
  completedAt, 
  scheduledAt, 
  status,
  enhanced = true 
}: JobTimelineProps) {
  const timelineEvents = [
    {
      label: "Created",
      date: createdAt,
      icon: CalendarIcon,
      tone: "subdued" as const,
      completed: true
    },
    ...(scheduledAt ? [{
      label: "Scheduled",
      date: scheduledAt,
      icon: ClockIcon,
      tone: "info" as const,
      completed: true
    }] : []),
    ...(startedAt ? [{
      label: "Started",
      date: startedAt,
      icon: PlayIcon,
      tone: "warning" as const,
      completed: true
    }] : []),
    ...(completedAt ? [{
      label: status === 'completed' ? "Completed" : status === 'failed' ? "Failed" : "Finished",
      date: completedAt,
      icon: status === 'failed' ? AlertTriangleIcon : PlayIcon,
      tone: status === 'completed' ? "success" as const : status === 'failed' ? "critical" as const : "subdued" as const,
      completed: true
    }] : [])
  ];

  const duration = startedAt && completedAt ? formatDuration(startedAt, completedAt) : null;

  return (
    <Card>
      <BlockStack gap="400">
        <Text variant="headingMd" as="h3">
          Timeline
        </Text>
        
        <BlockStack gap="200">
          {timelineEvents.map((event, index) => (
            <InlineStack key={index} gap="100" blockAlign="center" wrap={false}>
              <div style={{ minWidth: '16px', display: 'flex', alignItems: 'center' }}>
                <Icon source={event.icon} tone={event.tone} />
              </div>
              <Text variant="bodySm" as="span">
                <strong>{event.label}:</strong> {formatRelativeTime(event.date)}
              </Text>
              {enhanced && (
                <Text variant="bodySm" tone="subdued" as="span">
                  ({event.date.toLocaleString()})
                </Text>
              )}
            </InlineStack>
          ))}
        </BlockStack>
        
        {duration && (
          <Card background="bg-surface-secondary">
            <Text variant="bodySm" tone="subdued" as="p">
              <strong>Execution Time:</strong> {duration}
            </Text>
          </Card>
        )}
      </BlockStack>
    </Card>
  );
}

// 🦠 BACTERIAL JOB DATA VIEWER - Enhanced data display
export function JobDataViewer({ data, onCopy, enhanced = true }: JobDataViewerProps) {
  const { parsed, formatted, isValid } = parseJobData(data);
  
  const handleCopyFormatted = () => {
    onCopy(formatted, "Job data copied to clipboard!");
  };

  const handleCopyRaw = () => {
    onCopy(data, "Raw job data copied to clipboard!");
  };

  return (
    <Card>
      <BlockStack gap="400">
        <InlineStack align="space-between" blockAlign="center">
          <Text variant="headingMd" as="h3">
            Job Data
          </Text>
          <InlineStack gap="200">
            <Button
              size="slim"
              onClick={handleCopyFormatted}
              icon={ClipboardIcon}
            >
              Copy Formatted
            </Button>
            {enhanced && (
              <Button
                size="slim"
                variant="secondary"
                onClick={handleCopyRaw}
                icon={ClipboardIcon}
              >
                Copy Raw
              </Button>
            )}
          </InlineStack>
        </InlineStack>
        
        {!isValid && (
          <Card background="bg-surface-secondary">
            <InlineStack gap="100" blockAlign="center">
              <Icon source={AlertTriangleIcon} tone="critical" />
              <Text variant="bodySm" tone="critical" as="span">
                Invalid JSON data - displaying as raw text
              </Text>
            </InlineStack>
          </Card>
        )}
        
        {enhanced && isValid && parsed && (
          <Card background="bg-surface-secondary">
            <BlockStack gap="200">
              <Text variant="bodyMd" fontWeight="medium" as="h4">
                Data Summary
              </Text>
              <Text variant="bodySm" tone="subdued" as="p">
                {Object.keys(parsed).length} parameter{Object.keys(parsed).length === 1 ? '' : 's'}
              </Text>
              {Object.keys(parsed).slice(0, 3).map(key => (
                <Text key={key} variant="bodySm" tone="subdued" as="p">
                  • <strong>{key}:</strong> {typeof parsed[key]} 
                  {Array.isArray(parsed[key]) ? ` (${parsed[key].length} items)` : ''}
                </Text>
              ))}
              {Object.keys(parsed).length > 3 && (
                <Text variant="bodySm" tone="subdued" as="p">
                  ... and {Object.keys(parsed).length - 3} more
                </Text>
              )}
            </BlockStack>
          </Card>
        )}
        
        <div style={{ 
          maxHeight: '400px', 
          overflowY: 'auto', 
          background: 'var(--p-color-bg-surface-secondary)', 
          padding: 'var(--p-space-300)', 
          borderRadius: 'var(--p-border-radius-100)',
          fontFamily: 'monospace',
          fontSize: '12px',
          lineHeight: '1.4'
        }}>
          <pre style={{ 
            whiteSpace: 'pre-wrap', 
            wordBreak: 'break-all',
            margin: 0
          }}>
            {formatted}
          </pre>
        </div>
      </BlockStack>
    </Card>
  );
}

// 🦠 BACTERIAL JOB DETAILS - Main details component
export function JobDetailsCard({
  id,
  shop,
  type,
  status,
  createdAt,
  retryCount,
  maxRetries,
  errorMessage,
  onCopy
}: JobDetailsProps) {
  const retryProgress = getRetryProgress(retryCount, maxRetries);
  
  return (
    <Card>
      <BlockStack gap="400">
        <InlineStack align="space-between" blockAlign="start">
          <Text variant="headingMd" as="h2">
            Job Details
          </Text>
          <Badge tone={status === 'completed' ? 'success' : status === 'failed' ? 'critical' : status === 'processing' ? 'warning' : 'info'}>
            {status}
          </Badge>
        </InlineStack>
        
        <BlockStack gap="300">
          {/* Job ID with copy */}
          <InlineStack gap="200" blockAlign="center">
            <Text variant="bodyMd" as="p">
              <strong>ID:</strong> {id}
            </Text>
            <Button
              size="micro"
              icon={ClipboardIcon}
              onClick={() => onCopy(id, "Job ID copied to clipboard!")}
            />
          </InlineStack>
          
          {/* Basic info */}
          <Text variant="bodyMd" as="p">
            <strong>Shop:</strong> {shop}
          </Text>
          
          <Text variant="bodyMd" as="p">
            <strong>Type:</strong> {formatJobName(type)}
          </Text>
          
          <Text variant="bodyMd" as="p">
            <strong>Created:</strong> {formatRelativeTime(createdAt)}
          </Text>
          
          {/* Retry progress */}
          {maxRetries > 0 && (
            <InlineStack gap="200" blockAlign="center">
              <Text variant="bodyMd" as="p">
                <strong>Retries:</strong> {retryCount} / {maxRetries}
              </Text>
              {retryCount > 0 && (
                <Tooltip content={retryProgress.label}>
                  <Badge tone={retryProgress.color}>
                    {`${Math.round(retryProgress.percentage)}%`}
                  </Badge>
                </Tooltip>
              )}
            </InlineStack>
          )}
        </BlockStack>
        
        {/* Error/Success message */}
        {errorMessage && (
          (() => {
            const messageInfo = getMessageDisplayInfo(errorMessage, status);
            return (
              <Card background={messageInfo.background}>
                <InlineStack gap="100" blockAlign="start">
                  <Icon source={messageInfo.icon} tone={messageInfo.tone} />
                  <Text variant="bodySm" tone={messageInfo.tone} as="span">
                    <strong>{messageInfo.label}:</strong> {errorMessage}
                  </Text>
                </InlineStack>
              </Card>
            );
          })()
        )}
      </BlockStack>
    </Card>
  );
}
