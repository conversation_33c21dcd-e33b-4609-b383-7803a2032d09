// 🦠 BACTERIAL TASK CARD COMPONENT
// Self-contained, reusable task card with enhanced UX

import { Card, BlockStack, InlineStack, Text, Button, Badge, Icon, Tooltip } from "@shopify/polaris";
import { CheckIcon, ClockIcon, SettingsIcon, PlayIcon } from "@shopify/polaris-icons";
import type { JobType } from "@prisma/client";

// 🦠 BACTERIAL INTERFACES - Pure, self-contained
export interface TaskCardProps {
  /** Task ID */
  id: number;
  /** Task title */
  title: string;
  /** Task description */
  description: string;
  /** Task category */
  category: string;
  /** Task type */
  type: JobType;
  /** Task trigger description */
  trigger: string;
  /** Whether task is installed */
  isInstalled: boolean;
  /** Configure URL for non-installed tasks */
  configureUrl?: string;
  /** Manage URL for installed tasks */
  manageUrl?: string;
  /** Configure action handler (fallback if no URL) */
  onConfigure?: (taskId: number) => void;
  /** Manage action handler (fallback if no URL) */
  onManage?: (taskType: JobType) => void;
  /** Optional preview action handler */
  onPreview?: (taskId: number) => void;
  /** Link component to use (e.g., Remix Link) */
  LinkComponent?: any;
  /** Show enhanced features */
  enhanced?: boolean;
}

// 🦠 BACTERIAL CATEGORY COLORS - Pure mapping
const CATEGORY_COLORS: Record<string, "success" | "info" | "warning" | "critical" | "attention"> = {
  products: "success",
  orders: "info", 
  customers: "warning",
  inventory: "attention",
  marketing: "critical"
};

// 🦠 BACTERIAL CATEGORY ICONS - Pure mapping
const CATEGORY_ICONS: Record<string, any> = {
  products: SettingsIcon,
  orders: CheckIcon,
  customers: PlayIcon,
  inventory: ClockIcon,
  marketing: PlayIcon
};

// 🦠 BACTERIAL UTILITY - Get category color
export function getCategoryColor(category: string): "success" | "info" | "warning" | "critical" | "attention" {
  return CATEGORY_COLORS[category.toLowerCase()] || "info";
}

// 🦠 BACTERIAL UTILITY - Get category icon
export function getCategoryIcon(category: string) {
  return CATEGORY_ICONS[category.toLowerCase()] || SettingsIcon;
}

// 🦠 BACTERIAL UTILITY - Truncate text
export function truncateText(text: string, maxLength: number = 120): string {
  if (text.length <= maxLength) return text;
  return text.slice(0, maxLength).trim() + "...";
}

// 🦠 BACTERIAL TASK CARD - Enhanced, reusable
export function TaskCard({
  id,
  title,
  description,
  category,
  type,
  trigger,
  isInstalled,
  configureUrl,
  manageUrl,
  onConfigure,
  onManage,
  onPreview,
  LinkComponent,
  enhanced = true
}: TaskCardProps) {
  const categoryColor = getCategoryColor(category);
  const CategoryIcon = getCategoryIcon(category);

  // 🦠 BACTERIAL ACTION HANDLER - Pure function
  const handleMainAction = () => {
    if (isInstalled) {
      if (manageUrl && LinkComponent) {
        // URL navigation will be handled by LinkComponent wrapper
        return;
      } else if (onManage) {
        onManage(type);
      }
    } else {
      if (configureUrl && LinkComponent) {
        // URL navigation will be handled by LinkComponent wrapper
        return;
      } else if (onConfigure) {
        onConfigure(id);
      }
    }
  };

  // 🦠 BACTERIAL BUTTON CONTENT - Pure component
  const mainButton = (
    <Button
      variant={isInstalled ? "secondary" : "primary"}
      onClick={(!configureUrl && !manageUrl) ? handleMainAction : undefined}
      icon={isInstalled ? SettingsIcon : undefined}
      size={enhanced ? "medium" : "large"}
      fullWidth={!enhanced}
    >
      {isInstalled ? "Manage" : "Configure & Add"}
    </Button>
  );

  // 🦠 BACTERIAL LINK WRAPPER - Conditional wrapper
  const mainActionButton = (configureUrl || manageUrl) && LinkComponent ? (
    <LinkComponent
      to={isInstalled ? manageUrl : configureUrl}
      style={{ textDecoration: 'none', display: 'block' }}
    >
      {mainButton}
    </LinkComponent>
  ) : mainButton;

  return (
    <Card>
      <BlockStack gap="300">
        {/* Header with category badge and status */}
        <InlineStack align="space-between" blockAlign="start">
          <Badge tone={categoryColor} icon={CategoryIcon}>
            {category.charAt(0).toUpperCase() + category.slice(1)}
          </Badge>
          
          {isInstalled && (
            <Tooltip content="This automation is installed and active">
              <Badge tone="success" icon={CheckIcon}>
                Installed
              </Badge>
            </Tooltip>
          )}
        </InlineStack>

        {/* Title */}
        <Text variant="headingMd" as="h3">
          {title}
        </Text>

        {/* Description */}
        <Text variant="bodyMd" as="p" tone="subdued">
          {enhanced ? truncateText(description) : description}
        </Text>

        {/* Trigger info */}
        {enhanced && (
          <InlineStack gap="100" blockAlign="center" wrap={false}>
            <div style={{ minWidth: '16px', display: 'flex', alignItems: 'center' }}>
              <Icon source={ClockIcon} tone="subdued" />
            </div>
            <Text variant="bodySm" tone="subdued" as="span">
              Triggers: {trigger}
            </Text>
          </InlineStack>
        )}

        {/* Actions */}
        <InlineStack gap="200" align={enhanced ? "space-between" : "end"}>
          {enhanced && onPreview && !isInstalled && (
            <Button
              variant="tertiary"
              size="slim"
              onClick={() => onPreview(id)}
              icon={PlayIcon}
            >
              Preview
            </Button>
          )}

          {mainActionButton}
        </InlineStack>
      </BlockStack>
    </Card>
  );
}

// 🦠 BACTERIAL TASK GRID - Simple wrapper for consistent spacing
export interface TaskGridProps {
  /** Array of tasks */
  tasks: Array<TaskCardProps>;
  /** Enhanced card features */
  enhanced?: boolean;
}

// Note: Use with Polaris Grid.Cell for responsive layout
export function TaskGrid({ tasks, enhanced = true }: TaskGridProps) {
  return (
    <>
      {tasks.map(task => (
        <TaskCard key={task.id} {...task} enhanced={enhanced} />
      ))}
    </>
  );
}

// 🦠 BACTERIAL EMPTY STATE - Reusable empty state
export interface EmptyTasksProps {
  /** Custom message */
  message?: string;
  /** Show action button */
  showAction?: boolean;
  /** Action button text */
  actionText?: string;
  /** Action handler */
  onAction?: () => void;
}

export function EmptyTasks({ 
  message = "No tasks found matching your criteria.",
  showAction = false,
  actionText = "Clear filters",
  onAction 
}: EmptyTasksProps) {
  return (
    <Card>
      <BlockStack gap="300" align="center">
        <Text variant="bodyMd" as="p" alignment="center">
          {message}
        </Text>
        {showAction && onAction && (
          <Button variant="secondary" onClick={onAction}>
            {actionText}
          </Button>
        )}
      </BlockStack>
    </Card>
  );
}

// 🦠 BACTERIAL LOADING CARDS - Skeleton loading state
export function LoadingTaskCard() {
  return (
    <Card>
      <BlockStack gap="300">
        <div style={{ 
          height: '20px', 
          backgroundColor: '#f0f0f0', 
          borderRadius: '4px',
          width: '60%'
        }} />
        <div style={{ 
          height: '16px', 
          backgroundColor: '#f0f0f0', 
          borderRadius: '4px',
          width: '100%'
        }} />
        <div style={{ 
          height: '16px', 
          backgroundColor: '#f0f0f0', 
          borderRadius: '4px',
          width: '80%'
        }} />
        <div style={{ 
          height: '36px', 
          backgroundColor: '#f0f0f0', 
          borderRadius: '4px',
          width: '100%'
        }} />
      </BlockStack>
    </Card>
  );
}

export function LoadingTaskGrid({ count = 6 }: { count?: number }) {
  return (
    <>
      {Array.from({ length: count }, (_, i) => (
        <div key={i} style={{ gridColumn: 'span 4' }}>
          <LoadingTaskCard />
        </div>
      ))}
    </>
  );
}
