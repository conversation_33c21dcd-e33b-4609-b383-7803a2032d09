// 🦠 BACTERIAL WELCOME EXPERIENCE COMPONENTS
// Self-contained, reusable welcome and onboarding

import { useState, useEffect } from "react";
import { Banner, Card, BlockStack, InlineStack, Text, Button, Icon } from "@shopify/polaris";
import { StarIcon, PlayIcon, BookIcon, SettingsIcon } from "@shopify/polaris-icons";

// 🦠 BACTERIAL INTERFACES - Pure, self-contained
export interface WelcomeBannerProps {
  /** Banner title */
  title?: string;
  /** Banner message */
  message?: string;
  /** Primary action */
  primaryAction?: {
    content: string;
    url?: string;
    onAction?: () => void;
  };
  /** Secondary action */
  secondaryAction?: {
    content: string;
    url?: string;
    onAction?: () => void;
  };
  /** Dismiss handler */
  onDismiss?: () => void;
  /** Storage key for persistence */
  storageKey?: string;
  /** Auto-dismiss after time */
  autoDismissAfter?: number;
}

export interface QuickStartProps {
  /** Quick start steps */
  steps: Array<{
    title: string;
    description: string;
    icon?: any;
    action?: {
      content: string;
      url?: string;
      onAction?: () => void;
    };
    completed?: boolean;
  }>;
  /** Show progress */
  showProgress?: boolean;
  /** Enhanced features */
  enhanced?: boolean;
}

export interface OnboardingCardProps {
  /** Card title */
  title: string;
  /** Card description */
  description: string;
  /** Card icon */
  icon?: any;
  /** Primary action */
  action?: {
    content: string;
    url?: string;
    onAction?: () => void;
    variant?: "primary" | "secondary" | "tertiary";
  };
  /** Completion status */
  completed?: boolean;
  /** Enhanced features */
  enhanced?: boolean;
}

// 🦠 BACTERIAL UTILITY - Local storage management
export function useLocalStorage(key: string, defaultValue: any) {
  const [value, setValue] = useState(defaultValue);
  
  useEffect(() => {
    try {
      const item = localStorage.getItem(key);
      if (item) {
        setValue(JSON.parse(item));
      }
    } catch (error) {
      console.warn(`Error reading localStorage key "${key}":`, error);
    }
  }, [key]);
  
  const setStoredValue = (newValue: any) => {
    try {
      setValue(newValue);
      localStorage.setItem(key, JSON.stringify(newValue));
    } catch (error) {
      console.warn(`Error setting localStorage key "${key}":`, error);
    }
  };
  
  return [value, setStoredValue];
}

// 🦠 BACTERIAL UTILITY - Calculate completion percentage
export function calculateCompletionPercentage(steps: Array<{ completed?: boolean }>): number {
  const completedSteps = steps.filter(step => step.completed).length;
  return steps.length > 0 ? (completedSteps / steps.length) * 100 : 0;
}

// 🦠 BACTERIAL WELCOME BANNER - Dismissible welcome message
export function WelcomeBanner({
  title = "Welcome to the Automation Engine",
  message = "Automate repetitive tasks in your store with our pre-built workflows. Get started by browsing the Task Library.",
  primaryAction = { content: "Browse Task Library", url: "/app/library" },
  secondaryAction,
  onDismiss,
  storageKey = "welcomeBannerDismissed",
  autoDismissAfter
}: WelcomeBannerProps) {
  const [isDismissed, setIsDismissed] = useLocalStorage(storageKey, false);
  
  useEffect(() => {
    if (autoDismissAfter && !isDismissed) {
      const timer = setTimeout(() => {
        handleDismiss();
      }, autoDismissAfter);
      
      return () => clearTimeout(timer);
    }
  }, [autoDismissAfter, isDismissed]);
  
  const handleDismiss = () => {
    setIsDismissed(true);
    onDismiss?.();
  };
  
  if (isDismissed) {
    return null;
  }
  
  return (
    <Banner
      title={title}
      action={primaryAction}
      secondaryAction={secondaryAction}
      onDismiss={handleDismiss}
    >
      <p>{message}</p>
    </Banner>
  );
}

// 🦠 BACTERIAL ONBOARDING CARD - Individual onboarding step
export function OnboardingCard({
  title,
  description,
  icon,
  action,
  completed = false,
  enhanced = true
}: OnboardingCardProps) {
  return (
    <Card>
      <BlockStack gap="300">
        {/* Header with icon and completion status */}
        <InlineStack align="space-between" blockAlign="start">
          <InlineStack gap="200" blockAlign="center">
            {icon && (
              <div style={{ minWidth: '20px', display: 'flex', alignItems: 'center' }}>
                <Icon source={icon} tone={completed ? 'success' : 'subdued'} />
              </div>
            )}
            <Text variant="headingMd" as="h3">
              {title}
            </Text>
          </InlineStack>
          
          {completed && enhanced && (
            <Icon source={StarIcon} tone="success" />
          )}
        </InlineStack>
        
        {/* Description */}
        <Text variant="bodyMd" as="p" tone={completed ? 'subdued' : undefined}>
          {description}
        </Text>
        
        {/* Action button */}
        {action && !completed && (
          <InlineStack align="start">
            <Button
              variant={action.variant || "primary"}
              url={action.url}
              onClick={action.onAction}
            >
              {action.content}
            </Button>
          </InlineStack>
        )}
        
        {/* Completion indicator */}
        {completed && enhanced && (
          <Card background="bg-surface-success">
            <InlineStack gap="100" blockAlign="center">
              <Icon source={StarIcon} tone="success" />
              <Text variant="bodySm" tone="success" as="span">
                Completed
              </Text>
            </InlineStack>
          </Card>
        )}
      </BlockStack>
    </Card>
  );
}

// 🦠 BACTERIAL QUICK START - Onboarding checklist
export function QuickStart({
  steps,
  showProgress = true,
  enhanced = true
}: QuickStartProps) {
  const completionPercentage = calculateCompletionPercentage(steps);
  const completedCount = steps.filter(step => step.completed).length;
  
  return (
    <Card>
      <BlockStack gap="400">
        {/* Header with progress */}
        <BlockStack gap="200">
          <InlineStack align="space-between" blockAlign="center">
            <Text variant="headingMd" as="h2">
              Quick Start Guide
            </Text>
            {showProgress && (
              <Text variant="bodySm" tone="subdued" as="span">
                {completedCount} of {steps.length} completed
              </Text>
            )}
          </InlineStack>
          
          {showProgress && enhanced && (
            <div style={{
              width: '100%',
              height: '8px',
              backgroundColor: 'var(--p-color-bg-surface-secondary)',
              borderRadius: '4px',
              overflow: 'hidden'
            }}>
              <div style={{
                width: `${completionPercentage}%`,
                height: '100%',
                backgroundColor: 'var(--p-color-bg-success)',
                transition: 'width 0.3s ease'
              }} />
            </div>
          )}
        </BlockStack>
        
        {/* Steps */}
        <BlockStack gap="300">
          {steps.map((step, index) => (
            <InlineStack key={index} gap="200" blockAlign="start">
              {/* Step indicator */}
              <div style={{
                width: '24px',
                height: '24px',
                borderRadius: '50%',
                backgroundColor: step.completed ? 'var(--p-color-bg-success)' : 'var(--p-color-bg-surface-secondary)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                flexShrink: 0,
                marginTop: '2px'
              }}>
                {step.completed ? (
                  <Icon source={StarIcon} tone="success" />
                ) : (
                  <Text variant="bodySm" fontWeight="medium" as="span">
                    {index + 1}
                  </Text>
                )}
              </div>
              
              {/* Step content */}
              <BlockStack gap="100" style={{ flex: 1 }}>
                <Text variant="bodyMd" fontWeight="medium" as="h4" tone={step.completed ? 'subdued' : undefined}>
                  {step.title}
                </Text>
                <Text variant="bodySm" as="p" tone="subdued">
                  {step.description}
                </Text>
                
                {step.action && !step.completed && (
                  <InlineStack align="start">
                    <Button
                      size="slim"
                      variant="tertiary"
                      url={step.action.url}
                      onClick={step.action.onAction}
                    >
                      {step.action.content}
                    </Button>
                  </InlineStack>
                )}
              </BlockStack>
            </InlineStack>
          ))}
        </BlockStack>
        
        {/* Completion message */}
        {completionPercentage === 100 && enhanced && (
          <Card background="bg-surface-success">
            <InlineStack gap="200" blockAlign="center">
              <Icon source={StarIcon} tone="success" />
              <BlockStack gap="050">
                <Text variant="bodyMd" fontWeight="medium" tone="success" as="p">
                  Congratulations!
                </Text>
                <Text variant="bodySm" tone="success" as="p">
                  You've completed the quick start guide. You're ready to automate your store!
                </Text>
              </BlockStack>
            </InlineStack>
          </Card>
        )}
      </BlockStack>
    </Card>
  );
}

// 🦠 BACTERIAL HOOK - Welcome experience state
export function useWelcomeExperience() {
  const [showWelcome, setShowWelcome] = useLocalStorage("showWelcomeBanner", true);
  const [onboardingProgress, setOnboardingProgress] = useLocalStorage("onboardingProgress", {});
  
  const dismissWelcome = () => {
    setShowWelcome(false);
  };
  
  const markStepCompleted = (stepId: string) => {
    setOnboardingProgress({
      ...onboardingProgress,
      [stepId]: true
    });
  };
  
  const isStepCompleted = (stepId: string) => {
    return onboardingProgress[stepId] || false;
  };
  
  return {
    showWelcome,
    dismissWelcome,
    markStepCompleted,
    isStepCompleted,
    onboardingProgress
  };
}
