// 🦠 BACTERIAL AUTOMATION FILTERS COMPONENT
// Self-contained, reusable automation filtering and search

import { useState, useMemo } from "react";
import { Card, BlockStack, InlineStack, Text, Button, Badge, ChoiceList, Collapsible } from "@shopify/polaris";
import { FilterIcon } from "@shopify/polaris-icons";
import { SearchBar } from "./SearchBar";
import type { JobType } from "@prisma/client";

// 🦠 BACTERIAL INTERFACES - Pure, self-contained
export interface AutomationFilterProps {
  /** Search query */
  searchQuery: string;
  /** Selected status filters */
  statusFilters: string[];
  /** Selected type filters */
  typeFilters: string[];
  /** Show only running automations */
  showOnlyRunning: boolean;
  /** Search change handler */
  onSearchChange: (query: string) => void;
  /** Status filter change handler */
  onStatusFilterChange: (statuses: string[]) => void;
  /** Type filter change handler */
  onTypeFilterChange: (types: string[]) => void;
  /** Running filter change handler */
  onRunningFilterChange: (showOnlyRunning: boolean) => void;
  /** Clear all filters handler */
  onClearFilters: () => void;
}

export interface AutomationWithFilters {
  id: number;
  name: string;
  status: string;
  type: JobType;
  running: boolean;
  trigger: string;
  lastRunAt: Date | null;
}

// 🦠 BACTERIAL FILTER OPTIONS - Pure configuration
const STATUS_OPTIONS = [
  { label: "Active", value: "Active" },
  { label: "Inactive", value: "Inactive" },
  { label: "Paused", value: "Paused" }
];

const TYPE_OPTIONS = [
  { label: "UTM Order Tagging", value: "AUTO_TAG_ORDERS_UTM" },
  { label: "Collection Tagging", value: "ORDER_COLLECTION_TAG" },
  { label: "Collection Visibility", value: "COLLECTION_VISIBILITY_UPDATE" },
  { label: "Customer Vendor Tagging", value: "AUTO_TAG_CUSTOMER_BY_VENDOR" },
  { label: "Cart Attribute Tagging", value: "AUTO_TAG_ORDER_CART_ATTRIBUTE" }
];

const RUNNING_OPTIONS = [
  { label: "Show only running automations", value: "running" }
];

// 🦠 BACTERIAL UTILITY - Check if filters are active
export function hasActiveFilters(filters: Omit<AutomationFilterProps, 'onSearchChange' | 'onStatusFilterChange' | 'onTypeFilterChange' | 'onRunningFilterChange' | 'onClearFilters'>): boolean {
  return !!(
    filters.searchQuery.trim() ||
    filters.statusFilters.length > 0 ||
    filters.typeFilters.length > 0 ||
    filters.showOnlyRunning
  );
}

// 🦠 BACTERIAL UTILITY - Filter automations
export function filterAutomations(
  automations: AutomationWithFilters[],
  filters: Omit<AutomationFilterProps, 'onSearchChange' | 'onStatusFilterChange' | 'onTypeFilterChange' | 'onRunningFilterChange' | 'onClearFilters'>
): AutomationWithFilters[] {
  return automations.filter(automation => {
    // Search filter
    if (filters.searchQuery.trim()) {
      const query = filters.searchQuery.toLowerCase();
      const matchesSearch = 
        automation.name.toLowerCase().includes(query) ||
        automation.trigger.toLowerCase().includes(query) ||
        automation.type.toLowerCase().includes(query);
      
      if (!matchesSearch) return false;
    }
    
    // Status filter
    if (filters.statusFilters.length > 0) {
      if (!filters.statusFilters.includes(automation.status)) return false;
    }
    
    // Type filter
    if (filters.typeFilters.length > 0) {
      if (!filters.typeFilters.includes(automation.type)) return false;
    }
    
    // Running filter
    if (filters.showOnlyRunning) {
      if (!automation.running) return false;
    }
    
    return true;
  });
}

// 🦠 BACTERIAL UTILITY - Get filter summary
export function getFilterSummary(
  totalCount: number,
  filteredCount: number,
  filters: Omit<AutomationFilterProps, 'onSearchChange' | 'onStatusFilterChange' | 'onTypeFilterChange' | 'onRunningFilterChange' | 'onClearFilters'>
): string {
  if (!hasActiveFilters(filters)) {
    return `${totalCount} automation${totalCount === 1 ? '' : 's'}`;
  }
  
  return `${filteredCount} of ${totalCount} automation${totalCount === 1 ? '' : 's'}`;
}

// 🦠 BACTERIAL AUTOMATION FILTERS - Main component
export function AutomationFilters({
  searchQuery,
  statusFilters,
  typeFilters,
  showOnlyRunning,
  onSearchChange,
  onStatusFilterChange,
  onTypeFilterChange,
  onRunningFilterChange,
  onClearFilters
}: AutomationFilterProps) {
  const [filtersOpen, setFiltersOpen] = useState(false);
  const activeFiltersCount = useMemo(() => {
    let count = 0;
    if (statusFilters.length > 0) count++;
    if (typeFilters.length > 0) count++;
    if (showOnlyRunning) count++;
    return count;
  }, [statusFilters.length, typeFilters.length, showOnlyRunning]);

  const hasFilters = hasActiveFilters({
    searchQuery,
    statusFilters,
    typeFilters,
    showOnlyRunning
  });

  return (
    <Card>
      <BlockStack gap="300">
        {/* Search Bar */}
        <SearchBar
          value={searchQuery}
          onSearch={onSearchChange}
          placeholder="Search automations..."
          label="Search Automations"
        />
        
        {/* Filter Toggle */}
        <InlineStack align="space-between" blockAlign="center">
          <InlineStack gap="200" blockAlign="center">
            <Button
              variant="tertiary"
              onClick={() => setFiltersOpen(!filtersOpen)}
              icon={FilterIcon}
            >
              Filters
            </Button>
            {activeFiltersCount > 0 && (
              <Badge tone="info" size="small">
                {activeFiltersCount.toString()}
              </Badge>
            )}
          </InlineStack>
          
          {hasFilters && (
            <Button
              variant="plain"
              onClick={onClearFilters}
              size="micro"
            >
              Clear all filters
            </Button>
          )}
        </InlineStack>
        
        {/* Collapsible Filters */}
        <Collapsible open={filtersOpen} id="automation-filters">
          <BlockStack gap="400">
            {/* Status Filter */}
            <ChoiceList
              title="Status"
              choices={STATUS_OPTIONS}
              selected={statusFilters}
              onChange={onStatusFilterChange}
              allowMultiple
            />
            
            {/* Type Filter */}
            <ChoiceList
              title="Automation Type"
              choices={TYPE_OPTIONS}
              selected={typeFilters}
              onChange={onTypeFilterChange}
              allowMultiple
            />
            
            {/* Running Filter */}
            <ChoiceList
              title="Running Status"
              choices={RUNNING_OPTIONS}
              selected={showOnlyRunning ? ["running"] : []}
              onChange={(selected) => onRunningFilterChange(selected.includes("running"))}
            />
          </BlockStack>
        </Collapsible>
      </BlockStack>
    </Card>
  );
}

// 🦠 BACTERIAL FILTER SUMMARY - Display filter results
export interface FilterSummaryProps {
  /** Total automation count */
  totalCount: number;
  /** Filtered automation count */
  filteredCount: number;
  /** Current filters */
  filters: Omit<AutomationFilterProps, 'onSearchChange' | 'onStatusFilterChange' | 'onTypeFilterChange' | 'onRunningFilterChange' | 'onClearFilters'>;
}

export function FilterSummary({ totalCount, filteredCount, filters }: FilterSummaryProps) {
  const summary = getFilterSummary(totalCount, filteredCount, filters);
  
  return (
    <InlineStack align="space-between" blockAlign="center">
      <Text variant="bodySm" tone="subdued" as="span">
        {summary}
      </Text>

      {hasActiveFilters(filters) && (
        <InlineStack gap="100">
          {filters.searchQuery.trim() && (
            <Badge>{`Search: "${filters.searchQuery}"`}</Badge>
          )}
          {filters.statusFilters.length > 0 && (
            <Badge>{`Status: ${filters.statusFilters.length}`}</Badge>
          )}
          {filters.typeFilters.length > 0 && (
            <Badge>{`Type: ${filters.typeFilters.length}`}</Badge>
          )}
          {filters.showOnlyRunning && (
            <Badge>Running only</Badge>
          )}
        </InlineStack>
      )}
    </InlineStack>
  );
}

// 🦠 BACTERIAL HOOK - Automation filter state management
export function useAutomationFilters() {
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilters, setStatusFilters] = useState<string[]>([]);
  const [typeFilters, setTypeFilters] = useState<string[]>([]);
  const [showOnlyRunning, setShowOnlyRunning] = useState(false);

  const clearFilters = () => {
    setSearchQuery("");
    setStatusFilters([]);
    setTypeFilters([]);
    setShowOnlyRunning(false);
  };

  const filters = {
    searchQuery,
    statusFilters,
    typeFilters,
    showOnlyRunning
  };

  return {
    filters,
    setSearchQuery,
    setStatusFilters,
    setTypeFilters,
    setShowOnlyRunning,
    clearFilters,
    hasActiveFilters: hasActiveFilters(filters)
  };
}
