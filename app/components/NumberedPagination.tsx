import { Button, InlineStack, Text } from "@shopify/polaris";
import { ChevronLeftIcon, ChevronRightIcon } from "@shopify/polaris-icons";
import {
  generatePaginationRange,
  shouldShowPagination,
  isCurrentPage,
  isPreviousDisabled,
  isNextDisabled,
  getPreviousPage,
  getNextPage,
  isDots,
  PAGINATION_CONSTANTS
} from "../utils/paginationUtils";

interface NumberedPaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  pageNeighbours?: number;
}

export function NumberedPagination({
  currentPage,
  totalPages,
  onPageChange,
  pageNeighbours = PAGINATION_CONSTANTS.DEFAULT_NEIGHBOURS,
}: NumberedPaginationProps) {
  // Use bacterial utility to check if pagination should be shown
  if (!shouldShowPagination(totalPages)) {
    return null;
  }

  const pageNumbers = generatePaginationRange(currentPage, totalPages, pageNeighbours);

  return (
    <InlineStack gap={PAGINATION_CONSTANTS.BUTTON_GAP} align="center">
      <Button
        icon={ChevronLeftIcon}
        onClick={() => onPageChange(getPreviousPage(currentPage))}
        disabled={isPreviousDisabled(currentPage)}
        accessibilityLabel="Previous page"
      />

      {pageNumbers.map((page, index) => {
        // Use bacterial utility to check if item is dots
        if (isDots(page)) {
          return (
            <span key={`${PAGINATION_CONSTANTS.DOTS}-${index}`} style={{ padding: PAGINATION_CONSTANTS.DOT_SPACING }}>
              <Text as="span" tone="subdued">{PAGINATION_CONSTANTS.DOTS}</Text>
            </span>
          );
        }

        return (
          <Button
            key={page}
            onClick={() => {
              if (typeof page === 'number') {
                onPageChange(page);
              }
            }}
            pressed={isCurrentPage(page, currentPage)}
            disabled={isCurrentPage(page, currentPage)}
            aria-current={isCurrentPage(page, currentPage) ? "page" : undefined}
          >
            {page.toString()}
          </Button>
        );
      })}

      <Button
        icon={ChevronRightIcon}
        onClick={() => onPageChange(getNextPage(currentPage, totalPages))}
        disabled={isNextDisabled(currentPage, totalPages)}
        accessibilityLabel="Next page"
      />
    </InlineStack>
  );
}