// app/components/automation-configs/CancelHighRiskOrderConfig.tsx

import { BlockStack, Checkbox, TextField } from "@shopify/polaris";
import type { AutomationConfigProps } from "./types";
import {
  type CancelHighRiskOrderConfig,
  getTextFields,
  getCheckboxFields
} from "../../utils/order-cancellation";
import { createFormHandler } from "../../utils/form";

export function CancelHighRiskOrderConfig({ config, onConfigChange }: AutomationConfigProps<CancelHighRiskOrderConfig>) {
  // Use bacterial utility to create form handler
  const handleConfigChange = createFormHandler(config, onConfigChange);

  // Get field configurations from bacterial utilities
  const textFields = getTextFields();
  const checkboxFields = getCheckboxFields();

  return (
    <BlockStack gap="400">
      {/* Render text fields dynamically using bacterial utilities */}
      {textFields.map(field => (
        <TextField
          key={field.key}
          label={field.label}
          value={config[field.key as keyof CancelHighRiskOrderConfig] as string}
          onChange={(value) => handleConfigChange(field.key as keyof CancelHighRiskOrderConfig, value)}
          helpText={field.helpText}
          autoComplete="off"
        />
      ))}

      {/* Render checkbox fields dynamically using bacterial utilities */}
      {checkboxFields.map(field => (
        <Checkbox
          key={field.key}
          label={field.label}
          checked={config[field.key as keyof CancelHighRiskOrderConfig] as boolean}
          onChange={(checked) => handleConfigChange(field.key as keyof CancelHighRiskOrderConfig, checked)}
        />
      ))}
    </BlockStack>
  );
}