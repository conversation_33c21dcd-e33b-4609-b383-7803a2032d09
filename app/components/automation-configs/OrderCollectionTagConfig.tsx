import { <PERSON><PERSON><PERSON>, Button, InlineStack, BlockStack, Text } from "@shopify/polaris";
import { DeleteIcon } from "@shopify/polaris-icons";
import type { AutomationConfigProps } from "./types";
import { useState, useEffect, useCallback, useRef } from "react";
import { deepArrayEquals, removeIds, addIds } from "../../utils/array";
import {
  updateItemById,
  removeItemById,
  addItemWithId,
  ensureMinimumItems,
  lastItemMatches,
  areAllFieldsEmpty
} from "../../utils/form";

interface CollectionTagPair {
  id: string;
  key: string;
  value: string;
}

interface CollectionTagConfig {
  collections_and_tags: Omit<CollectionTagPair, 'id'>[];
}

// Using bacterial utility for deep array comparison

export function OrderCollectionTagConfig({ config, onConfigChange }: AutomationConfigProps<CollectionTagConfig>) {
  const [pairs, setPairs] = useState<CollectionTagPair[]>([]);
  const pairsWithoutIdsRef = useRef<Omit<CollectionTagPair, 'id'>[]>([]);

  // Keep ref updated with current pairs (without IDs) using bacterial utility
  useEffect(() => {
    pairsWithoutIdsRef.current = removeIds(pairs);
  }, [pairs]);

  // Sync state with props using bacterial utilities
  useEffect(() => {
    const incoming = config?.collections_and_tags ?? [];

    // Only update if incoming data is different using bacterial utility
    if (!deepArrayEquals(incoming, pairsWithoutIdsRef.current)) {
      const newPairs = incoming.length > 0
        ? addIds(incoming, 'pair')
        : ensureMinimumItems([], () => ({ key: "", value: "" }), 'pair');

      setPairs(newPairs);
    }
  }, [config?.collections_and_tags]);

  const handleAddPair = useCallback(() => {
    // Prevent adding empty rows consecutively using bacterial utility
    if (lastItemMatches(pairs, pair => areAllFieldsEmpty(pair, ['key', 'value']))) {
      return;
    }

    // Add new pair using bacterial utility
    const newPairs = addItemWithId(pairs, { key: "", value: "" }, 'pair');
    setPairs(newPairs);
    onConfigChange({
      collections_and_tags: removeIds(newPairs)
    });
  }, [pairs, onConfigChange]);

  const handleRemovePair = useCallback((idToRemove: string) => {
    // Remove item and ensure minimum items using bacterial utilities
    const removedPairs = removeItemById(pairs, idToRemove);
    const finalPairs = ensureMinimumItems(removedPairs, () => ({ key: "", value: "" }), 'pair');

    setPairs(finalPairs);
    onConfigChange({
      collections_and_tags: removeIds(finalPairs)
    });
  }, [pairs, onConfigChange]);

  const handlePairChange = useCallback((idToChange: string, field: 'key' | 'value', newValue: string) => {
    // Update item using bacterial utility
    const newPairs = updateItemById(pairs, idToChange, { [field]: newValue });

    setPairs(newPairs);
    onConfigChange({
      collections_and_tags: removeIds(newPairs)
    });
  }, [pairs, onConfigChange]);

  return (
    <BlockStack gap="400">
      <Text variant="headingMd" as="h3">Collections and Tags Mapping</Text>
      <Text as="p" tone="subdued">
        For each row, specify a collection identifier on the left and the comma-separated tags you want to apply on the right.
      </Text>
      <BlockStack gap="300">
        {pairs.map((pair, index) => (
          <InlineStack key={pair.id} gap="200" blockAlign="center" wrap={false}>
            <div style={{ flex: 1 }}>
              <TextField
                label="Collection Identifier (Title, Handle, or ID)"
                value={pair.key}
                onChange={(value) => handlePairChange(pair.id, "key", value)}
                placeholder="e.g., 'Best Sellers' or 'best-sellers'"
                autoComplete="off"
                helpText={index === 0 ? undefined : " "} // Maintain consistent height
              />
            </div>
            <div style={{ flex: 1 }}>
              <TextField
                label="Tags to Apply (comma-separated)"
                value={pair.value}
                onChange={(value) => handlePairChange(pair.id, "value", value)}
                placeholder="e.g., vip, special-order"
                autoComplete="off"
                helpText={index === 0 ? undefined : " "} // Maintain consistent height
              />
            </div>
            <Button
              icon={DeleteIcon}
              accessibilityLabel={`Remove row ${index + 1}`}
              onClick={() => handleRemovePair(pair.id)}
              disabled={pairs.length === 1 && areAllFieldsEmpty(pair, ['key', 'value'])}
            />
          </InlineStack>
        ))}
      </BlockStack>
      <Button onClick={handleAddPair}>Add Row</Button>
    </BlockStack>
  );
}