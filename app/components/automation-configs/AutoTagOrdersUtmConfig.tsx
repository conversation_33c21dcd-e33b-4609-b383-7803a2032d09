import { Checkbox, BlockStack } from "@shopify/polaris";
import type { AutomationConfigProps } from "./types";
import {
  type UtmTaggingConfig,
  UTM_CONFIG_KEYS,
  getUtmParameterLabel
} from "../../utils/utm";
import { createCheckboxHandler } from "../../utils/form";

export function AutoTagOrdersUtmConfig({ config, onConfigChange }: AutomationConfigProps<UtmTaggingConfig>) {
  // Use bacterial utility to create checkbox handler
  const handleCheckboxChange = createCheckboxHandler(config, onConfigChange);

  return (
    <BlockStack gap="400">
      {/* Use bacterial utilities to generate checkboxes dynamically */}
      {UTM_CONFIG_KEYS.map(key => (
        <Checkbox
          key={key}
          label={getUtmParameterLabel(key)}
          checked={config[key]}
          onChange={(checked) => handleCheckboxChange(key, checked)}
        />
      ))}
    </BlockStack>
  );
}