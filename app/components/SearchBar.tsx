// 🦠 BACTERIAL SEARCH BAR COMPONENT
// Self-contained, reusable search bar with all UX enhancements

import { useState, useRef, useEffect } from "react";
import { TextField, BlockStack, InlineStack, Icon, Spinner, Button, Text } from "@shopify/polaris";
import { SearchIcon } from "@shopify/polaris-icons";

// 🦠 BACTERIAL INTERFACES - Pure, self-contained
export interface SearchBarProps {
  /** Current search value */
  value: string;
  /** Called when search value changes (debounced) */
  onSearch: (value: string) => void;
  /** Called when search is cleared */
  onClear?: () => void;
  /** Placeholder text */
  placeholder?: string;
  /** Whether search is in progress */
  isLoading?: boolean;
  /** Debounce delay in milliseconds */
  debounceMs?: number;
  /** Show keyboard shortcut hint */
  showKeyboardHint?: boolean;
  /** Custom label for accessibility */
  label?: string;
  /** Additional help text */
  helpText?: string;
}

export interface SearchSummaryProps {
  /** Current search query */
  query: string;
  /** Total results count */
  resultCount: number;
  /** Total available items when no search */
  totalCount?: number;
  /** Additional context (e.g., category filter) */
  context?: string;
}

export interface ClearFiltersProps {
  /** Whether any filters are active */
  hasActiveFilters: boolean;
  /** Called when clear filters is clicked */
  onClearFilters: () => void;
  /** Button text */
  buttonText?: string;
}

// 🦠 BACTERIAL SEARCH BAR - Pure, reusable component
export function SearchBar({
  value,
  onSearch,
  onClear,
  placeholder = "Search...",
  isLoading = false,
  debounceMs = 300,
  showKeyboardHint = true,
  label = "Search",
  helpText
}: SearchBarProps) {
  const [localValue, setLocalValue] = useState(value);
  const timeoutRef = useRef<NodeJS.Timeout>();

  // Sync with external value changes
  useEffect(() => {
    setLocalValue(value);
  }, [value]);

  // 🦠 BACTERIAL DEBOUNCE - Self-contained, no external dependencies
  const performSearch = (searchValue: string) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    
    timeoutRef.current = setTimeout(() => {
      onSearch(searchValue);
    }, debounceMs);
  };

  // 🦠 BACTERIAL CLEAR FUNCTION - Pure, simple
  const handleClear = () => {
    setLocalValue("");
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    onSearch("");
    onClear?.();
  };

  // 🦠 BACTERIAL KEYBOARD SHORTCUTS - Self-contained
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Cmd/Ctrl + K to focus search
      if ((event.metaKey || event.ctrlKey) && event.key === 'k') {
        event.preventDefault();
        const searchField = document.querySelector(`input[placeholder*="${placeholder.split('(')[0].trim()}"]`) as HTMLInputElement;
        if (searchField) {
          searchField.focus();
          searchField.select();
        }
      }
      
      // Escape to clear search when focused
      if (event.key === 'Escape' && event.target instanceof HTMLInputElement && 
          event.target.placeholder?.includes(placeholder.split('(')[0].trim())) {
        handleClear();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [placeholder]);

  const displayPlaceholder = showKeyboardHint && !placeholder.includes('⌘') 
    ? `${placeholder} (⌘K)` 
    : placeholder;

  return (
    <div style={{ 
      transition: 'all 0.2s ease',
      transform: isLoading ? 'scale(1.01)' : 'scale(1)',
      opacity: isLoading ? 0.8 : 1
    }}>
      <TextField
        label={label}
        labelHidden
        value={localValue}
        onChange={(newValue) => {
          setLocalValue(newValue);
          performSearch(newValue);
        }}
        placeholder={displayPlaceholder}
        autoComplete="off"
        prefix={<Icon source={SearchIcon} />}
        clearButton={localValue ? true : false}
        onClearButtonClick={handleClear}
        helpText={helpText || (localValue && !isLoading ? "Press Escape to clear search" : undefined)}
      />
    </div>
  );
}

// 🦠 BACTERIAL SEARCH SUMMARY - Pure, reusable
export function SearchSummary({ 
  query, 
  resultCount, 
  totalCount, 
  context 
}: SearchSummaryProps) {
  const hasSearch = query.trim().length > 0;
  const hasContext = context && context !== "all";
  
  if (!hasSearch && !hasContext) {
    return totalCount ? (
      <Text variant="bodySm" tone="subdued" as="p">
        {totalCount} items available
      </Text>
    ) : null;
  }

  const parts: string[] = [];
  if (hasSearch) parts.push(`"${query}"`);
  if (hasContext) parts.push(`in ${context}`);
  
  const searchText = parts.length > 0 ? ` for ${parts.join(" ")}` : "";
  const itemText = resultCount === 1 ? "result" : "results";
  
  return (
    <Text variant="bodySm" tone="subdued" as="p">
      {resultCount} {itemText}{searchText}
    </Text>
  );
}

// 🦠 BACTERIAL CLEAR FILTERS - Pure, reusable
export function ClearFiltersButton({ 
  hasActiveFilters, 
  onClearFilters, 
  buttonText = "Clear all filters" 
}: ClearFiltersProps) {
  if (!hasActiveFilters) return null;
  
  return (
    <Button
      onClick={onClearFilters}
      variant="plain"
      size="micro"
    >
      {buttonText}
    </Button>
  );
}

// 🦠 BACTERIAL SEARCH CONTAINER - Combines all pieces
export interface SearchContainerProps extends SearchBarProps {
  /** Search summary props */
  summary?: SearchSummaryProps;
  /** Clear filters props */
  clearFilters?: ClearFiltersProps;
  /** Show loading spinner in summary */
  showLoadingInSummary?: boolean;
}

export function SearchContainer({
  summary,
  clearFilters,
  showLoadingInSummary = true,
  isLoading,
  ...searchBarProps
}: SearchContainerProps) {
  return (
    <BlockStack gap="300">
      <SearchBar {...searchBarProps} isLoading={isLoading} />
      
      {(summary || clearFilters) && (
        <InlineStack align="space-between" blockAlign="center">
          <InlineStack gap="200" blockAlign="center">
            {showLoadingInSummary && isLoading && <Spinner size="small" />}
            {summary && <SearchSummary {...summary} />}
          </InlineStack>
          
          {clearFilters && <ClearFiltersButton {...clearFilters} />}
        </InlineStack>
      )}
    </BlockStack>
  );
}

// 🦠 BACTERIAL HOOK - Optional convenience hook
export function useSearchState(initialValue = "", debounceMs = 300) {
  const [value, setValue] = useState(initialValue);
  const [isLoading, setIsLoading] = useState(false);
  
  const handleSearch = (newValue: string) => {
    setIsLoading(true);
    setValue(newValue);
    // Note: External code should handle the actual search and call setIsLoading(false)
  };
  
  const handleClear = () => {
    setValue("");
    setIsLoading(false);
  };
  
  return {
    value,
    isLoading,
    setIsLoading,
    handleSearch,
    handleClear
  };
}
