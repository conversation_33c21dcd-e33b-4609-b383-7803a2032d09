-- CreateTable
CREATE TABLE `Session` (
    `id` VARCHAR(191) NOT NULL,
    `shop` VARCHAR(191) NOT NULL,
    `state` VARCHAR(191) NOT NULL,
    `isOnline` B<PERSON>OLEAN NOT NULL DEFAULT false,
    `scope` VARCHAR(191) NULL,
    `expires` D<PERSON>ETIME(3) NULL,
    `accessToken` VARCHAR(191) NOT NULL,
    `userId` BIGINT NULL,
    `firstName` VARCHAR(191) NULL,
    `lastName` VARCHAR(191) NULL,
    `email` VARCHAR(191) NULL,
    `accountOwner` B<PERSON><PERSON>EAN NOT NULL DEFAULT false,
    `locale` VARCHAR(191) NULL,
    `collaborator` B<PERSON><PERSON>EAN NULL DEFAULT false,
    `emailVerified` BOOLEAN NULL DEFAULT false,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `Job` (
    `id` VARCHAR(191) NOT NULL,
    `shop` VARCHAR(191) NOT NULL,
    `type` ENUM('COLLECTION_VISIBILITY_UPDATE', 'AUTO_TAG_ORDERS_UTM', 'ORDER_COLLECTION_TAG', 'ORDER_CART_ATTRIBUTE_TAG', 'AUTO_TAG_CUSTOMER_BY_ORDER_TAG', 'AUTO_TAG_CUSTOMER_BY_VENDOR', 'COLLECTION_VISIBILITY_BULK_UPDATE', 'STUCK_JOB_CLEANUP', 'ORDER_DISCOUNT_TAG', 'CANCEL_HIGH_RISK_ORDER') NOT NULL,
    `status` ENUM('pending', 'processing', 'completed', 'failed', 'retrying', 'canceled', 'scheduled') NOT NULL DEFAULT 'pending',
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,
    `data` VARCHAR(191) NOT NULL,
    `retryCount` INTEGER NOT NULL DEFAULT 0,
    `maxRetries` INTEGER NOT NULL DEFAULT 3,
    `errorMessage` VARCHAR(191) NULL,
    `completedAt` DATETIME(3) NULL,
    `startedAt` DATETIME(3) NULL,
    `lockedAt` DATETIME(3) NULL,
    `lockedById` VARCHAR(191) NULL,
    `sessionId` VARCHAR(191) NULL,
    `scheduledAt` DATETIME(3) NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `Automation` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `shop` VARCHAR(191) NOT NULL,
    `name` VARCHAR(191) NOT NULL,
    `type` ENUM('COLLECTION_VISIBILITY_UPDATE', 'AUTO_TAG_ORDERS_UTM', 'ORDER_COLLECTION_TAG', 'ORDER_CART_ATTRIBUTE_TAG', 'AUTO_TAG_CUSTOMER_BY_ORDER_TAG', 'AUTO_TAG_CUSTOMER_BY_VENDOR', 'COLLECTION_VISIBILITY_BULK_UPDATE', 'STUCK_JOB_CLEANUP', 'ORDER_DISCOUNT_TAG', 'CANCEL_HIGH_RISK_ORDER') NOT NULL,
    `status` VARCHAR(191) NOT NULL DEFAULT 'Inactive',
    `trigger` VARCHAR(191) NOT NULL,
    `lastRunAt` DATETIME(3) NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,
    `config` JSON NULL,

    INDEX `Automation_shop_idx`(`shop`),
    UNIQUE INDEX `Automation_shop_type_key`(`shop`, `type`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `TaskTemplate` (
    `id` INTEGER NOT NULL,
    `title` VARCHAR(191) NOT NULL,
    `description` VARCHAR(191) NOT NULL,
    `category` VARCHAR(191) NOT NULL,
    `type` ENUM('COLLECTION_VISIBILITY_UPDATE', 'AUTO_TAG_ORDERS_UTM', 'ORDER_COLLECTION_TAG', 'ORDER_CART_ATTRIBUTE_TAG', 'AUTO_TAG_CUSTOMER_BY_ORDER_TAG', 'AUTO_TAG_CUSTOMER_BY_VENDOR', 'COLLECTION_VISIBILITY_BULK_UPDATE', 'STUCK_JOB_CLEANUP', 'ORDER_DISCOUNT_TAG', 'CANCEL_HIGH_RISK_ORDER') NOT NULL,
    `trigger` VARCHAR(191) NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `TaskTemplate_type_key`(`type`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `AppSetting` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `shop` VARCHAR(191) NOT NULL,
    `settings` JSON NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `AppSetting_shop_key`(`shop`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
