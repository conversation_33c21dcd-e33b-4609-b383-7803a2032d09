// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

// Note that some adapters may set a maximum length for the String type by default, please ensure your strings are long
// enough when changing adapters.
// See https://www.prisma.io/docs/orm/reference/prisma-schema-reference#string for more information
datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model Session {
  id            String    @id
  shop          String
  state         String
  isOnline      <PERSON>an   @default(false)
  scope         String?
  expires       DateTime?
  accessToken   String
  userId        BigInt?
  firstName     String?
  lastName      String?
  email         String?
  accountOwner  Bo<PERSON>an   @default(false)
  locale        String?
  collaborator  <PERSON><PERSON><PERSON>?  @default(false)
  emailVerified <PERSON>olean?  @default(false)
}

enum JobType {
  COLLECTION_VISIBILITY_UPDATE
  AUTO_TAG_ORDERS_UTM
  ORDER_COLLECTION_TAG
  ORDER_CART_ATTRIBUTE_TAG
  AUTO_TAG_CUSTOMER_BY_ORDER_TAG
  AUTO_TAG_CUSTOMER_BY_VENDOR
  COLLECTION_VISIBILITY_BULK_UPDATE
  STUCK_JOB_CLEANUP
  ORDER_DISCOUNT_TAG
  CANCEL_HIGH_RISK_ORDER
}

enum JobStatus {
  pending
  processing
  completed
  failed
  retrying
  canceled
  scheduled
}

// Model for managing background jobs for automations like collection visibility
model Job {
  id            String    @id @default(uuid())
  shop          String    // The shop domain (e.g., my-store.myshopify.com)
  type          JobType    // Type of job, e.g., "COLLECTION_VISIBILITY_UPDATE"
  status       JobStatus  @default(pending)
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  data          String // JSON string of job-specific data
  retryCount    Int       @default(0)
  maxRetries    Int       @default(3)
  errorMessage  String? // Stores error message if job fails
  completedAt   DateTime?
  startedAt     DateTime? // Timestamp for when processing began
  lockedAt      DateTime?
  lockedById    String?
  sessionId     String?   // Kept for potential other uses, but not for worker auth
  scheduledAt   DateTime? // Timestamp when the job is scheduled to run (UTC)
}

model Automation {
  id        Int      @id @default(autoincrement())
  shop      String
  name      String
  type      JobType  // Links this automation to a specific job type
  status    String   @default("Inactive") // "Active" or "Inactive"
  trigger   String
  lastRunAt DateTime?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  config    Json?     // Configuration for the automation, e.g., UTM tagging options

  @@index([shop])
  @@unique([shop, type]) // Ensures a shop can only have one automation of a specific type
}

model TaskTemplate {
  id          Int     @id // Manually assigned ID from TASKS_LIBRARY, used in URLs
  title       String
  description String  @db.Text // Use TEXT type for longer descriptions
  category    String
  type        JobType @unique // Each JobType should only have one template
  trigger     String

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model AppSetting {
  id        Int      @id @default(autoincrement())
  shop      String   @unique
  settings  Json
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}
