tasks if the week

-Shopify Automation Engine (Mechanic Clone):
Optimizing the code for Best practices

-Shopify Automation Engine (Mechanic Clone):
Optimizing the code for Best practices and improving the UX/UX along the way

-Shopify Automation Engine (Mechanic Clone):
Switched the database from sqlite to mysql and started doing research for deployment

-Shopify Automation Engine (Mechanic Clone):
Deploying the app on the production instance and also update the code due to incompatible node versions

-Shopify Automation Engine (Mechanic Clone):
Finished deploying the app on the production instance and also fixed bugs discovered during post-deployment testing


project: shopify Automation engine
 - I optimized the code to follow best practices, allow easy maintance and code reusability
 - I optimized the code for better performance and reduced the number of API calls to Shopify by 80%
 - I improved the UX/UX by adding more descriptive error messages, better form validation, and improved feedback to the user
 - I switched the database from sqlite to mysql to allow for better performance and scalability
 - I deployed the app on the production instance and fixed bugs discovered during post-deployment testing